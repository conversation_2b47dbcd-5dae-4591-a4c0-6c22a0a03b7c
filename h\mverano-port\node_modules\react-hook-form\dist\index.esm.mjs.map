{"version": 3, "file": "index.esm.mjs", "sources": ["../src/utils/isCheckBoxInput.ts", "../src/utils/isDateObject.ts", "../src/utils/isNullOrUndefined.ts", "../src/utils/isObject.ts", "../src/logic/getEventValue.ts", "../src/logic/getNodeParentName.ts", "../src/logic/isNameInFieldArray.ts", "../src/utils/isPlainObject.ts", "../src/utils/isWeb.ts", "../src/utils/cloneObject.ts", "../src/utils/compact.ts", "../src/utils/isUndefined.ts", "../src/utils/get.ts", "../src/utils/isBoolean.ts", "../src/utils/isKey.ts", "../src/utils/stringToPath.ts", "../src/utils/set.ts", "../src/constants.ts", "../src/useFormContext.tsx", "../src/logic/getProxyFormState.ts", "../src/utils/isEmptyObject.ts", "../src/logic/shouldRenderFormState.ts", "../src/utils/convertToArrayPayload.ts", "../src/logic/shouldSubscribeByName.ts", "../src/useSubscribe.ts", "../src/useFormState.ts", "../src/utils/isString.ts", "../src/logic/generateWatchOutput.ts", "../src/useWatch.ts", "../src/useController.ts", "../src/controller.tsx", "../src/utils/flatten.ts", "../src/form.tsx", "../src/logic/appendErrors.ts", "../src/logic/generateId.ts", "../src/logic/getFocusFieldName.ts", "../src/logic/getValidationModes.ts", "../src/logic/isWatched.ts", "../src/logic/iterateFieldsByAction.ts", "../src/logic/updateFieldArrayRootError.ts", "../src/utils/isFileInput.ts", "../src/utils/isFunction.ts", "../src/utils/isHTMLElement.ts", "../src/utils/isMessage.ts", "../src/utils/isRadioInput.ts", "../src/utils/isRegex.ts", "../src/logic/getCheckboxValue.ts", "../src/logic/getRadioValue.ts", "../src/logic/getValidateError.ts", "../src/logic/getValueAndMessage.ts", "../src/logic/validateField.ts", "../src/utils/append.ts", "../src/utils/fillEmptyArray.ts", "../src/utils/insert.ts", "../src/utils/move.ts", "../src/utils/prepend.ts", "../src/utils/remove.ts", "../src/utils/swap.ts", "../src/utils/unset.ts", "../src/utils/update.ts", "../src/useFieldArray.ts", "../src/utils/createSubject.ts", "../src/utils/isPrimitive.ts", "../src/utils/deepEqual.ts", "../src/utils/isMultipleSelect.ts", "../src/utils/isRadioOrCheckbox.ts", "../src/utils/live.ts", "../src/utils/objectHasFunction.ts", "../src/logic/getDirtyFields.ts", "../src/logic/getFieldValueAs.ts", "../src/logic/getFieldValue.ts", "../src/logic/getResolverOptions.ts", "../src/logic/getRuleValue.ts", "../src/logic/hasPromiseValidation.ts", "../src/logic/hasValidation.ts", "../src/logic/schemaErrorLookup.ts", "../src/logic/skipValidation.ts", "../src/logic/unsetEmptyArray.ts", "../src/logic/createFormControl.ts", "../src/useForm.ts"], "sourcesContent": ["import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "import { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || data instanceof FileList)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : {};\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "export default (val: unknown): val is undefined => val === undefined;\n", "import compact from './compact';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\n\nexport default <T>(\n  object: T,\n  path?: string | null,\n  defaultValue?: unknown,\n): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = compact(path.split(/[,[\\].]+?/)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "export default (value: string) => /^\\w*$/.test(value);\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import { FieldPath, FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (\n  object: FieldValues,\n  path: FieldPath<FieldValues>,\n  value?: unknown,\n) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n\n    if (key === '__proto__') {\n      return;\n    }\n\n    object[key] = newValue;\n    object = object[key];\n  }\n  return object;\n};\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TransformedValues extends FieldValues | undefined = undefined,\n>(): UseFormReturn<TFieldValues, TContext, TransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues extends FieldValues | undefined = undefined,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <TFieldValues extends FieldValues, TContext = any>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import { VALIDATION_MODE } from '../constants';\nimport {\n  Control,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & { name?: InternalFieldName },\n  _proxyFormState: K,\n  updateFormState: Control<T>['_updateFormState'],\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import React from 'react';\n\nimport { Subject } from './utils/createSubject';\n\ntype Props<T> = {\n  disabled?: boolean;\n  subject: Subject<T>;\n  next: (value: T) => void;\n};\n\nexport function useSubscribe<T>(props: Props<T>) {\n  const _props = React.useRef(props);\n  _props.current = props;\n\n  React.useEffect(() => {\n    const subscription =\n      !props.disabled &&\n      _props.current.subject &&\n      _props.current.subject.subscribe({\n        next: _props.current.next,\n      });\n\n    return () => {\n      subscription && subscription.unsubscribe();\n    };\n  }, [props.disabled]);\n}\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport shouldRenderFormState from './logic/shouldRenderFormState';\nimport shouldSubscribeByName from './logic/shouldSubscribeByName';\nimport {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  UseFormStateProps,\n  UseFormStateReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState<TFieldValues extends FieldValues = FieldValues>(\n  props?: UseFormStateProps<TFieldValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _mounted = React.useRef(true);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n  const _name = React.useRef(name);\n\n  _name.current = name;\n\n  useSubscribe({\n    disabled,\n    next: (\n      value: Partial<FormState<TFieldValues>> & { name?: InternalFieldName },\n    ) =>\n      _mounted.current &&\n      shouldSubscribeByName(\n        _name.current as InternalFieldName,\n        value.name,\n        exact,\n      ) &&\n      shouldRenderFormState(\n        value,\n        _localProxyFormState.current,\n        control._updateFormState,\n      ) &&\n      updateFormState({\n        ...control._formState,\n        ...value,\n      }),\n    subject: control._subjects.state,\n  });\n\n  React.useEffect(() => {\n    _mounted.current = true;\n    _localProxyFormState.current.isValid && control._updateValid(true);\n\n    return () => {\n      _mounted.current = false;\n    };\n  }, [control]);\n\n  return getProxyFormState(\n    formState,\n    control,\n    _localProxyFormState.current,\n    false,\n  );\n}\n\nexport { useFormState };\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport shouldSubscribeByName from './logic/shouldSubscribeByName';\nimport cloneObject from './utils/cloneObject';\nimport {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(props: {\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n  } = props || {};\n  const _name = React.useRef(name);\n\n  _name.current = name;\n\n  useSubscribe({\n    disabled,\n    subject: control._subjects.values,\n    next: (formState: { name?: InternalFieldName; values?: FieldValues }) => {\n      if (\n        shouldSubscribeByName(\n          _name.current as InternalFieldName,\n          formState.name,\n          exact,\n        )\n      ) {\n        updateValue(\n          cloneObject(\n            generateWatchOutput(\n              _name.current as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              defaultValue,\n            ),\n          ),\n        );\n      }\n    },\n  });\n\n  const [value, updateValue] = React.useState(\n    control._getWatch(\n      name as InternalFieldName,\n      defaultValue as DeepPartialSkipArrayKey<TFieldValues>,\n    ),\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport set from './utils/set';\nimport { EVENTS } from './constants';\nimport {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(\n  props: UseControllerProps<TFieldValues, TName>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues>();\n  const { name, disabled, control = methods.control, shouldUnregister } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(\n      control._formValues,\n      name,\n      get(control._defaultValues, name, props.defaultValue),\n    ),\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n  const formState = useFormState({\n    control,\n    name,\n    exact: true,\n  });\n\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    if (get(control._fields, name)) {\n      control._updateDisabledField({\n        disabled,\n        fields: control._fields,\n        name,\n        value: get(control._fields, name)._f.value,\n      });\n    }\n  }, [disabled, name, control]);\n\n  return {\n    field: {\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange: React.useCallback(\n        (event) =>\n          _registerProps.current.onChange({\n            target: {\n              value: getEventValue(event),\n              name: name as InternalFieldName,\n            },\n            type: EVENTS.CHANGE,\n          }),\n        [name],\n      ),\n      onBlur: React.useCallback(\n        () =>\n          _registerProps.current.onBlur({\n            target: {\n              value: get(control._formValues, name),\n              name: name as InternalFieldName,\n            },\n            type: EVENTS.BLUR,\n          }),\n        [name, control],\n      ),\n      ref: React.useCallback(\n        (elm) => {\n          const field = get(control._fields, name);\n\n          if (field && elm) {\n            field._f.ref = {\n              focus: () => elm.focus(),\n              select: () => elm.select(),\n              setCustomValidity: (message: string) =>\n                elm.setCustomValidity(message),\n              reportValidity: () => elm.reportValidity(),\n            };\n          }\n        },\n        [control._fields, name],\n      ),\n    },\n    formState,\n    fieldState: Object.defineProperties(\n      {},\n      {\n        invalid: {\n          enumerable: true,\n          get: () => !!get(formState.errors, name),\n        },\n        isDirty: {\n          enumerable: true,\n          get: () => !!get(formState.dirtyFields, name),\n        },\n        isTouched: {\n          enumerable: true,\n          get: () => !!get(formState.touchedFields, name),\n        },\n        isValidating: {\n          enumerable: true,\n          get: () => !!get(formState.validatingFields, name),\n        },\n        error: {\n          enumerable: true,\n          get: () => get(formState.errors, name),\n        },\n      },\n    ) as ControllerFieldState,\n  };\n}\n", "import { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(\n  props: ControllerProps<TFieldValues, TName>,\n) => props.render(useController<TFieldValues, TName>(props));\n\nexport { Controller };\n", "import { FieldValues } from '../types';\n\nimport { isObjectType } from './isObject';\n\nexport const flatten = (obj: FieldValues) => {\n  const output: FieldValues = {};\n\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key])) {\n      const nested = flatten(obj[key]);\n\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n\n  return output;\n};\n", "import React from 'react';\n\nimport { flatten } from './utils/flatten';\nimport { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  T extends FieldValues,\n  U extends FieldValues | undefined = undefined,\n>(props: FormProps<T, U>) {\n  const methods = useFormContext<T>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      const flattenFormValues = flatten(control._formValues);\n\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(action, {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? { 'Content-Type': encType } : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default () => {\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import { VALIDATION_MODE } from '../constants';\nimport { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField as FieldRefs, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nexport default iterateFieldsByAction;\n", "import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import { FieldError, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n    disabled,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabled) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n            ? inputValue > maxOutput.value\n            : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n            ? inputValue < minOutput.value\n            : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n      ? [path]\n      : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends\n    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n>(\n  props: UseFieldArrayProps<TFieldValues, TFieldArrayName, TKeyName>,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  props.rules &&\n    (control as Control<TFieldValues>).register(\n      name as FieldPath<TFieldValues>,\n      props.rules as RegisterOptions<TFieldValues>,\n    );\n\n  useSubscribe({\n    next: ({\n      values,\n      name: fieldArrayName,\n    }: {\n      values?: FieldValues;\n      name?: InternalFieldName;\n    }) => {\n      if (fieldArrayName === _name.current || !fieldArrayName) {\n        const fieldValues = get(values, _name.current);\n        if (Array.isArray(fieldValues)) {\n          setFields(fieldValues);\n          ids.current = fieldValues.map(generateId);\n        }\n      }\n    },\n    subject: control._subjects.array,\n  });\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._updateFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._updateFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted)\n    ) {\n      if (control._options.resolver) {\n        control._executeSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.values.next({\n      name,\n      values: { ...control._formValues },\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._updateValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._updateFieldArray(name);\n\n    return () => {\n      (control._options.shouldUnregister || shouldUnregister) &&\n        control.unregister(name as FieldPath<TFieldValues>);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(object1: any, object2: any) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n      ? value === ''\n        ? NaN\n        : value\n          ? +value\n          : value\n      : valueAsDate && isString(value)\n        ? new Date(value)\n        : setValueAs\n          ? setValueAs(value)\n          : value;\n", "import { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (_f.refs ? _f.refs.every((ref) => ref.disabled) : ref.disabled) {\n    return;\n  }\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "import {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n      ? rule.source\n      : isObject(rule)\n        ? isRegex(rule.value)\n          ? rule.value.source\n          : rule.value\n        : rule;\n", "import { Field, Validate } from '../types';\nimport isFunction from '../utils/isFunction';\nimport isObject from '../utils/isObject';\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\n\nexport default (fieldReference: Field['_f']) =>\n  (!fieldReference || !fieldReference.validate) &&\n  !!(\n    (isFunction(fieldReference.validate) &&\n      fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n    (isObject(fieldReference.validate) &&\n      Object.values(fieldReference.validate).find(\n        (validateFunction: Validate<unknown, unknown>) =>\n          validateFunction.constructor.name === ASYNC_FUNCTION,\n      ))\n  );\n", "import { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport {\n  BatchFieldArrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  PathValue,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasPromiseValidation from './hasPromiseValidation';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n>(\n  props: UseFormProps<TFieldValues, TContext> = {},\n): Omit<UseFormReturn<TFieldValues, TContext>, 'formState'> {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  let _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.defaultValues || _options.values) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? {}\n    : cloneObject(_defaultValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    values: createSubject(),\n    array: createSubject(),\n    state: createSubject(),\n  };\n  const validationModeBeforeSubmit = getValidationModes(_options.mode);\n  const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _updateValid = async (shouldUpdateValid?: boolean) => {\n    if (!props.disabled && (_proxyFormState.isValid || shouldUpdateValid)) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _executeSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (names?: string[], isValidating?: boolean) => {\n    if (\n      !props.disabled &&\n      (_proxyFormState.isValidating || _proxyFormState.validatingFields)\n    ) {\n      (names || Array.from(_names.mount)).forEach((name) => {\n        if (name) {\n          isValidating\n            ? set(_formState.validatingFields, name, isValidating)\n            : unset(_formState.validatingFields, name);\n        }\n      });\n\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields),\n      });\n    }\n  };\n\n  const _updateFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method && !props.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        _proxyFormState.touchedFields &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _updateValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!props.disabled) {\n      const disabledField = !!(\n        get(_fields, name) &&\n        get(_fields, name)._f &&\n        get(_fields, name)._f.disabled\n      );\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n\n        const isCurrentFieldPristine =\n          disabledField || deepEqual(get(_defaultValues, name), fieldValue);\n\n        isPreviousDirty = !!(\n          !disabledField && get(_formState.dirtyFields, name)\n        );\n        isCurrentFieldPristine || disabledField\n          ? unset(_formState.dirtyFields, name)\n          : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          (_proxyFormState.dirtyFields &&\n            isPreviousDirty !== !isCurrentFieldPristine);\n      }\n\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField =\n            shouldUpdateField ||\n            (_proxyFormState.touchedFields &&\n              isPreviousFieldTouched !== isBlurEvent);\n        }\n      }\n\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      _proxyFormState.isValid &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (props.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(props.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n  };\n\n  const _executeSchema = async (name?: InternalFieldName[]) => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n    _updateIsValidating(name);\n    return result;\n  };\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _executeSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field as Field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction =\n            field._f && hasPromiseValidation((field as Field)._f);\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n\n          const fieldError = await validateField(\n            field as Field,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        !isEmptyObject(fieldValue) &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) =>\n    !props.disabled &&\n    (name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues));\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n            ? _defaultValues\n            : isString(names)\n              ? { [names]: defaultValue }\n              : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        props.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.length > 1\n              ? fieldReference.refs.forEach(\n                  (checkboxRef) =>\n                    (!checkboxRef.defaultChecked || !checkboxRef.disabled) &&\n                    (checkboxRef.checked = Array.isArray(fieldValue)\n                      ? !!(fieldValue as []).find(\n                          (data: string) => data === checkboxRef.value,\n                        )\n                      : fieldValue === checkboxRef.value),\n                )\n              : fieldReference.refs[0] &&\n                (fieldReference.refs[0].checked = !!fieldValue);\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.values.next({\n              name,\n              values: { ..._formValues },\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      const fieldValue = value[fieldKey];\n      const fieldName = `${name}.${fieldKey}`;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        isObject(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: { ..._formValues },\n      });\n\n      if (\n        (_proxyFormState.isDirty || _proxyFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState });\n    _subjects.values.next({\n      name: _state.mount ? name : undefined,\n      values: { ..._formValues },\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    _state.mount = true;\n    const target = event.target;\n    let name = target.name as string;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const getCurrentFieldValue = () =>\n      target.type ? getFieldValue(field._f) : getEventValue(event);\n    const _updateIsFieldValueUpdated = (fieldValue: any): void => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n        deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = getCurrentFieldValue();\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(\n        name,\n        fieldValue,\n        isBlurEvent,\n        false,\n      );\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.values.next({\n          name,\n          type: event.type,\n          values: { ..._formValues },\n        });\n\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid) {\n          if (props.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _updateValid();\n            }\n          } else {\n            _updateValid();\n          }\n        }\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      if (_options.resolver) {\n        const { errors } = await _executeSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (\n          await validateField(\n            field,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n        _updateIsValidating([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (_proxyFormState.isValid) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _updateValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      (_proxyFormState.isValid && isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n        ? get(values, fieldNames)\n        : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n\n    // Don't override existing error messages elsewhere in the object tree.\n    const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.values.subscribe({\n          next: (payload) =>\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.values.next({\n      values: { ..._formValues },\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _updateValid();\n  };\n\n  const _updateDisabledField: Control<TFieldValues>['_updateDisabledField'] = ({\n    disabled,\n    name,\n    field,\n    fields,\n    value,\n  }) => {\n    if ((isBoolean(disabled) && _state.mount) || !!disabled) {\n      const inputValue = disabled\n        ? undefined\n        : isUndefined(value)\n          ? getFieldValue(field ? field._f : get(fields, name)._f)\n          : value;\n      set(_formValues, name, inputValue);\n      updateTouchAndDirty(name, inputValue, false, false, true);\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined =\n      isBoolean(options.disabled) || isBoolean(props.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _updateDisabledField({\n        field,\n        disabled: isBoolean(options.disabled)\n          ? options.disabled\n          : props.disabled,\n        name,\n        value: options.value,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined\n        ? { disabled: options.disabled || props.disabled }\n        : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          const currentField: Field = get(_fields, name);\n          if (currentField) {\n            ref.disabled = currentField._f.disabled || disabled;\n\n            if (Array.isArray(currentField._f.refs)) {\n              currentField._f.refs.forEach((inputRef) => {\n                inputRef.disabled = currentField._f.disabled || disabled;\n              });\n            }\n          }\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        e.persist && e.persist();\n      }\n      let fieldValues = cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _executeSchema();\n        _formState.errors = errors;\n        fieldValues = values;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TFieldValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as PathValue<\n            TFieldValues,\n            FieldPath<TFieldValues>\n          >,\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _updateValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([\n          ..._names.mount,\n          ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n        ]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        _fields = {};\n      }\n\n      _formValues = props.shouldUnregister\n        ? keepStateOptions.keepDefaultValues\n          ? cloneObject(_defaultValues)\n          : {}\n        : cloneObject(values);\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.values.next({\n        values: { ...values },\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!props.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n          ? _formState.isDirty\n          : !!(\n              keepStateOptions.keepDefaultValues &&\n              !deepEqual(formValues, _defaultValues)\n            ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? {}\n        : keepStateOptions.keepDirtyValues\n          ? keepStateOptions.keepDefaultValues && _formValues\n            ? getDirtyFields(_defaultValues, _formValues)\n            : _formState.dirtyFields\n          : keepStateOptions.keepDefaultValues && formValues\n            ? getDirtyFields(_defaultValues, formValues)\n            : keepStateOptions.keepDirty\n              ? _formState.dirtyFields\n              : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? (formValues as Function)(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect && fieldRef.select();\n      }\n    }\n  };\n\n  const _updateFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    (_options.defaultValues as Function)().then((values: TFieldValues) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  return {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _executeSchema,\n      _getWatch,\n      _getDirty,\n      _updateValid,\n      _removeUnmounted,\n      _updateFieldArray,\n      _updateDisabledField,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _updateFormState,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      _setErrors,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      set _formState(value) {\n        _formState = value;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n}\n", "import React from 'react';\n\nimport { createFormControl } from './logic/createFormControl';\nimport getProxyFormState from './logic/getProxyFormState';\nimport shouldRenderFormState from './logic/shouldRenderFormState';\nimport deepEqual from './utils/deepEqual';\nimport isFunction from './utils/isFunction';\nimport {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  UseFormProps,\n  UseFormReturn,\n} from './types';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues extends FieldValues | undefined = undefined,\n>(\n  props: UseFormProps<TFieldValues, TContext> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >();\n  const _values = React.useRef<typeof props.values>();\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    _formControl.current = {\n      ...createFormControl(props),\n      formState,\n    };\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useSubscribe({\n    subject: control._subjects.state,\n    next: (\n      value: Partial<FormState<TFieldValues>> & { name?: InternalFieldName },\n    ) => {\n      if (\n        shouldRenderFormState(\n          value,\n          control._proxyFormState,\n          control._updateFormState,\n          true,\n        )\n      ) {\n        updateFormState({ ...control._formState });\n      }\n    },\n  });\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, control._options.resetOptions);\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [props.values, control]);\n\n  React.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n    }\n  }, [props.errors, control]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._updateValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.values.next({\n        values: control._getWatch(),\n      });\n  }, [props.shouldUnregister, control]);\n\n  React.useEffect(() => {\n    if (_formControl.current) {\n      _formControl.current.watch = _formControl.current.watch.bind({});\n    }\n  }, [formState]);\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "names": ["insert", "insertAt", "isCheckBox"], "mappings": ";;AAEA,sBAAe,CAAC,OAAqB,KACnC,OAAO,CAAC,IAAI,KAAK,UAAU;;ACH7B,mBAAe,CAAC,KAAc,KAAoB,KAAK,YAAY,IAAI;;ACAvE,wBAAe,CAAC,KAAc,KAAgC,KAAK,IAAI,IAAI;;ACGpE,MAAM,YAAY,GAAG,CAAC,KAAc,KACzC,OAAO,KAAK,KAAK,QAAQ,CAAC;AAE5B,eAAe,CAAmB,KAAc,KAC9C,CAAC,iBAAiB,CAAC,KAAK,CAAC;AACzB,IAAA,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;IACrB,YAAY,CAAC,KAAK,CAAC;AACnB,IAAA,CAAC,YAAY,CAAC,KAAK,CAAC;;ACLtB,oBAAe,CAAC,KAAc,KAC5B,QAAQ,CAAC,KAAK,CAAC,IAAK,KAAe,CAAC,MAAM;AACxC,MAAE,eAAe,CAAE,KAAe,CAAC,MAAM,CAAC;AACxC,UAAG,KAAe,CAAC,MAAM,CAAC,OAAO;AACjC,UAAG,KAAe,CAAC,MAAM,CAAC,KAAK;MAC/B,KAAK;;ACVX,wBAAe,CAAC,IAAY,KAC1B,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,IAAI;;ACGvD,yBAAe,CAAC,KAA6B,EAAE,IAAuB,KACpE,KAAK,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;;ACHpC,oBAAe,CAAC,UAAkB,KAAI;IACpC,MAAM,aAAa,GACjB,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC;AAE7D,IAAA,QACE,QAAQ,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC,EACxE;AACJ,CAAC;;ACTD,YAAe,OAAO,MAAM,KAAK,WAAW;AAC1C,IAAA,OAAO,MAAM,CAAC,WAAW,KAAK,WAAW;IACzC,OAAO,QAAQ,KAAK,WAAW;;ACET,SAAA,WAAW,CAAI,IAAO,EAAA;AAC5C,IAAA,IAAI,IAAS,CAAC;IACd,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAEpC,IAAA,IAAI,IAAI,YAAY,IAAI,EAAE;AACxB,QAAA,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;KACvB;AAAM,SAAA,IAAI,IAAI,YAAY,GAAG,EAAE;AAC9B,QAAA,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;KACtB;AAAM,SAAA,IACL,EAAE,KAAK,KAAK,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,QAAQ,CAAC,CAAC;SAC7D,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,EAC3B;QACA,IAAI,GAAG,OAAO,GAAG,EAAE,GAAG,EAAE,CAAC;QAEzB,IAAI,CAAC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YACpC,IAAI,GAAG,IAAI,CAAC;SACb;aAAM;AACL,YAAA,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;AACtB,gBAAA,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;oBAC5B,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;iBACpC;aACF;SACF;KACF;SAAM;AACL,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,OAAO,IAAI,CAAC;AACd;;AChCA,cAAe,CAAS,KAAe,KACrC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE;;ACDnD,kBAAe,CAAC,GAAY,KAAuB,GAAG,KAAK,SAAS;;ACKpE,UAAe,CACb,MAAS,EACT,IAAoB,EACpB,YAAsB,KACf;IACP,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC9B,QAAA,OAAO,YAAY,CAAC;KACrB;AAED,IAAA,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CACpD,CAAC,MAAM,EAAE,GAAG,KACV,iBAAiB,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,MAAM,CAAC,GAAe,CAAC,EAC9D,MAAM,CACP,CAAC;AAEF,IAAA,OAAO,WAAW,CAAC,MAAM,CAAC,IAAI,MAAM,KAAK,MAAM;AAC7C,UAAE,WAAW,CAAC,MAAM,CAAC,IAAe,CAAC,CAAC;AACpC,cAAE,YAAY;AACd,cAAE,MAAM,CAAC,IAAe,CAAC;UACzB,MAAM,CAAC;AACb,CAAC;;ACzBD,gBAAe,CAAC,KAAc,KAAuB,OAAO,KAAK,KAAK,SAAS;;ACA/E,YAAe,CAAC,KAAa,KAAK,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;;ACErD,mBAAe,CAAC,KAAa,KAC3B,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;;ACGxD,UAAe,CACb,MAAmB,EACnB,IAA4B,EAC5B,KAAe,KACb;AACF,IAAA,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;AACf,IAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;AAC3D,IAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC/B,IAAA,MAAM,SAAS,GAAG,MAAM,GAAG,CAAC,CAAC;AAE7B,IAAA,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE;AACvB,QAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,QAAQ,GAAG,KAAK,CAAC;AAErB,QAAA,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,YAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC7B,QAAQ;gBACN,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;AAC3C,sBAAE,QAAQ;sBACR,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAC5B,0BAAE,EAAE;0BACF,EAAE,CAAC;SACZ;AAED,QAAA,IAAI,GAAG,KAAK,WAAW,EAAE;YACvB,OAAO;SACR;AAED,QAAA,MAAM,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;AACvB,QAAA,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;KACtB;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;;ACtCM,MAAM,MAAM,GAAG;AACpB,IAAA,IAAI,EAAE,MAAM;AACZ,IAAA,SAAS,EAAE,UAAU;AACrB,IAAA,MAAM,EAAE,QAAQ;CACR,CAAC;AAEJ,MAAM,eAAe,GAAG;AAC7B,IAAA,MAAM,EAAE,QAAQ;AAChB,IAAA,QAAQ,EAAE,UAAU;AACpB,IAAA,QAAQ,EAAE,UAAU;AACpB,IAAA,SAAS,EAAE,WAAW;AACtB,IAAA,GAAG,EAAE,KAAK;CACF,CAAC;AAEJ,MAAM,sBAAsB,GAAG;AACpC,IAAA,GAAG,EAAE,KAAK;AACV,IAAA,GAAG,EAAE,KAAK;AACV,IAAA,SAAS,EAAE,WAAW;AACtB,IAAA,SAAS,EAAE,WAAW;AACtB,IAAA,OAAO,EAAE,SAAS;AAClB,IAAA,QAAQ,EAAE,UAAU;AACpB,IAAA,QAAQ,EAAE,UAAU;CACZ;;AClBV,MAAM,eAAe,GAAG,KAAK,CAAC,aAAa,CAAuB,IAAI,CAAC,CAAC;AAExE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACI,MAAM,cAAc,GAAG,MAK5B,KAAK,CAAC,UAAU,CAAC,eAAe,EAI9B;AAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACU,MAAA,YAAY,GAAG,CAK1B,KAAoE,KAClE;IACF,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC;AACpC,IAAA,QACE,KAAA,CAAA,aAAA,CAAC,eAAe,CAAC,QAAQ,EAAA,EAAC,KAAK,EAAE,IAAgC,EAAA,EAC9D,QAAQ,CACgB,EAC3B;AACJ;;ACvFA,wBAAe,CACb,SAAkC,EAClC,OAAwC,EACxC,mBAAmC,EACnC,MAAM,GAAG,IAAI,KACX;AACF,IAAA,MAAM,MAAM,GAAG;QACb,aAAa,EAAE,OAAO,CAAC,cAAc;KAClB,CAAC;AAEtB,IAAA,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE;AAC3B,QAAA,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE;YACjC,GAAG,EAAE,MAAK;gBACR,MAAM,IAAI,GAAG,GAA0D,CAAC;gBAExE,IAAI,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,eAAe,CAAC,GAAG,EAAE;AACzD,oBAAA,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,eAAe,CAAC,GAAG,CAAC;iBAChE;gBAED,mBAAmB,KAAK,mBAAmB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAC1D,gBAAA,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;aACxB;AACF,SAAA,CAAC,CAAC;KACJ;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;;ACzBD,oBAAe,CAAC,KAAc,KAC5B,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM;;ACK/C,4BAAe,CACb,aAAmE,EACnE,eAAkB,EAClB,eAA+C,EAC/C,MAAgB,KACd;IACF,eAAe,CAAC,aAAa,CAAC,CAAC;IAC/B,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,EAAE,GAAG,aAAa,CAAC;AAE7C,IAAA,QACE,aAAa,CAAC,SAAS,CAAC;AACxB,QAAA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM;AACpE,QAAA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CACzB,CAAC,GAAG,KACF,eAAe,CAAC,GAA0B,CAAC;aAC1C,CAAC,MAAM,IAAI,eAAe,CAAC,GAAG,CAAC,CACnC,EACD;AACJ,CAAC;;AC5BD,4BAAe,CAAI,KAAQ,MAAM,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;;ACExE,4BAAe,CACb,IAAQ,EACR,UAAmB,EACnB,KAAe,KAEf,CAAC,IAAI;AACL,IAAA,CAAC,UAAU;AACX,IAAA,IAAI,KAAK,UAAU;IACnB,qBAAqB,CAAC,IAAI,CAAC,CAAC,IAAI,CAC9B,CAAC,WAAW,KACV,WAAW;AACX,SAAC,KAAK;cACF,WAAW,KAAK,UAAU;AAC5B,cAAE,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC;AAClC,gBAAA,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAC1C;;ACPG,SAAU,YAAY,CAAI,KAAe,EAAA;IAC7C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACnC,IAAA,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;AAEvB,IAAA,KAAK,CAAC,SAAS,CAAC,MAAK;AACnB,QAAA,MAAM,YAAY,GAChB,CAAC,KAAK,CAAC,QAAQ;YACf,MAAM,CAAC,OAAO,CAAC,OAAO;AACtB,YAAA,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC;AAC/B,gBAAA,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI;AAC1B,aAAA,CAAC,CAAC;AAEL,QAAA,OAAO,MAAK;AACV,YAAA,YAAY,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC;AAC7C,SAAC,CAAC;AACJ,KAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;AACvB;;ACXA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACH,SAAS,YAAY,CACnB,KAAuC,EAAA;AAEvC,IAAA,MAAM,OAAO,GAAG,cAAc,EAAgB,CAAC;AAC/C,IAAA,MAAM,EAAE,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AACzE,IAAA,MAAM,CAAC,SAAS,EAAE,eAAe,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACxE,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpC,IAAA,MAAM,oBAAoB,GAAG,KAAK,CAAC,MAAM,CAAC;AACxC,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,SAAS,EAAE,KAAK;AAChB,QAAA,WAAW,EAAE,KAAK;AAClB,QAAA,aAAa,EAAE,KAAK;AACpB,QAAA,gBAAgB,EAAE,KAAK;AACvB,QAAA,YAAY,EAAE,KAAK;AACnB,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,MAAM,EAAE,KAAK;AACd,KAAA,CAAC,CAAC;IACH,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAEjC,IAAA,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;AAErB,IAAA,YAAY,CAAC;QACX,QAAQ;QACR,IAAI,EAAE,CACJ,KAAsE,KAEtE,QAAQ,CAAC,OAAO;YAChB,qBAAqB,CACnB,KAAK,CAAC,OAA4B,EAClC,KAAK,CAAC,IAAI,EACV,KAAK,CACN;YACD,qBAAqB,CACnB,KAAK,EACL,oBAAoB,CAAC,OAAO,EAC5B,OAAO,CAAC,gBAAgB,CACzB;AACD,YAAA,eAAe,CAAC;gBACd,GAAG,OAAO,CAAC,UAAU;AACrB,gBAAA,GAAG,KAAK;aACT,CAAC;AACJ,QAAA,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK;AACjC,KAAA,CAAC,CAAC;AAEH,IAAA,KAAK,CAAC,SAAS,CAAC,MAAK;AACnB,QAAA,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;QACxB,oBAAoB,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAEnE,QAAA,OAAO,MAAK;AACV,YAAA,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;AAC3B,SAAC,CAAC;AACJ,KAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;AAEd,IAAA,OAAO,iBAAiB,CACtB,SAAS,EACT,OAAO,EACP,oBAAoB,CAAC,OAAO,EAC5B,KAAK,CACN,CAAC;AACJ;;ACxGA,eAAe,CAAC,KAAc,KAAsB,OAAO,KAAK,KAAK,QAAQ;;ACI7E,0BAAe,CACb,KAAoC,EACpC,MAAa,EACb,UAAwB,EACxB,QAAkB,EAClB,YAAuC,KACrC;AACF,IAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;QACnB,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACpC,OAAO,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;KAC7C;AAED,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,QAAA,OAAO,KAAK,CAAC,GAAG,CACd,CAAC,SAAS,MACR,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,CACpE,CACF,CAAC;KACH;IAED,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;AAErC,IAAA,OAAO,UAAU,CAAC;AACpB,CAAC;;ACoGD;;;;;;;;;;;;;;;AAeG;AACG,SAAU,QAAQ,CACtB,KAAmC,EAAA;AAEnC,IAAA,MAAM,OAAO,GAAG,cAAc,EAAE,CAAC;AACjC,IAAA,MAAM,EACJ,OAAO,GAAG,OAAO,CAAC,OAAO,EACzB,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,KAAK,GACN,GAAG,KAAK,IAAI,EAAE,CAAC;IAChB,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAEjC,IAAA,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;AAErB,IAAA,YAAY,CAAC;QACX,QAAQ;AACR,QAAA,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,MAAM;AACjC,QAAA,IAAI,EAAE,CAAC,SAA6D,KAAI;AACtE,YAAA,IACE,qBAAqB,CACnB,KAAK,CAAC,OAA4B,EAClC,SAAS,CAAC,IAAI,EACd,KAAK,CACN,EACD;gBACA,WAAW,CACT,WAAW,CACT,mBAAmB,CACjB,KAAK,CAAC,OAAkD,EACxD,OAAO,CAAC,MAAM,EACd,SAAS,CAAC,MAAM,IAAI,OAAO,CAAC,WAAW,EACvC,KAAK,EACL,YAAY,CACb,CACF,CACF,CAAC;aACH;SACF;AACF,KAAA,CAAC,CAAC;AAEH,IAAA,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,GAAG,KAAK,CAAC,QAAQ,CACzC,OAAO,CAAC,SAAS,CACf,IAAyB,EACzB,YAAqD,CACtD,CACF,CAAC;IAEF,KAAK,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;AAElD,IAAA,OAAO,KAAK,CAAC;AACf;;AC1KA;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACG,SAAU,aAAa,CAI3B,KAA8C,EAAA;AAE9C,IAAA,MAAM,OAAO,GAAG,cAAc,EAAgB,CAAC;AAC/C,IAAA,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,gBAAgB,EAAE,GAAG,KAAK,CAAC;AAC9E,IAAA,MAAM,YAAY,GAAG,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACpE,MAAM,KAAK,GAAG,QAAQ,CAAC;QACrB,OAAO;QACP,IAAI;QACJ,YAAY,EAAE,GAAG,CACf,OAAO,CAAC,WAAW,EACnB,IAAI,EACJ,GAAG,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,EAAE,KAAK,CAAC,YAAY,CAAC,CACtD;AACD,QAAA,KAAK,EAAE,IAAI;AACZ,KAAA,CAAwC,CAAC;IAC1C,MAAM,SAAS,GAAG,YAAY,CAAC;QAC7B,OAAO;QACP,IAAI;AACJ,QAAA,KAAK,EAAE,IAAI;AACZ,KAAA,CAAC,CAAC;IAEH,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CACjC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;QACrB,GAAG,KAAK,CAAC,KAAK;QACd,KAAK;QACL,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC;AACnE,KAAA,CAAC,CACH,CAAC;AAEF,IAAA,KAAK,CAAC,SAAS,CAAC,MAAK;QACnB,MAAM,sBAAsB,GAC1B,OAAO,CAAC,QAAQ,CAAC,gBAAgB,IAAI,gBAAgB,CAAC;AAExD,QAAA,MAAM,aAAa,GAAG,CAAC,IAAuB,EAAE,KAAc,KAAI;YAChE,MAAM,KAAK,GAAU,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAEhD,YAAA,IAAI,KAAK,IAAI,KAAK,CAAC,EAAE,EAAE;AACrB,gBAAA,KAAK,CAAC,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC;aACxB;AACH,SAAC,CAAC;AAEF,QAAA,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAE1B,IAAI,sBAAsB,EAAE;AAC1B,YAAA,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;YACrE,GAAG,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACzC,YAAA,IAAI,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,EAAE;gBAC/C,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;aACvC;SACF;AAED,QAAA,OAAO,MAAK;AACV,YAAA,CACE,YAAY;kBACR,sBAAsB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM;kBAChD,sBAAsB;AAE1B,kBAAE,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;AAC1B,kBAAE,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACjC,SAAC,CAAC;KACH,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC,CAAC;AAEpD,IAAA,KAAK,CAAC,SAAS,CAAC,MAAK;QACnB,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;YAC9B,OAAO,CAAC,oBAAoB,CAAC;gBAC3B,QAAQ;gBACR,MAAM,EAAE,OAAO,CAAC,OAAO;gBACvB,IAAI;AACJ,gBAAA,KAAK,EAAE,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK;AAC3C,aAAA,CAAC,CAAC;SACJ;KACF,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;IAE9B,OAAO;AACL,QAAA,KAAK,EAAE;YACL,IAAI;YACJ,KAAK;YACL,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,QAAQ;kBACzC,EAAE,QAAQ,EAAE,SAAS,CAAC,QAAQ,IAAI,QAAQ,EAAE;kBAC5C,EAAE,CAAC;AACP,YAAA,QAAQ,EAAE,KAAK,CAAC,WAAW,CACzB,CAAC,KAAK,KACJ,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC;AAC9B,gBAAA,MAAM,EAAE;AACN,oBAAA,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC;AAC3B,oBAAA,IAAI,EAAE,IAAyB;AAChC,iBAAA;gBACD,IAAI,EAAE,MAAM,CAAC,MAAM;AACpB,aAAA,CAAC,EACJ,CAAC,IAAI,CAAC,CACP;AACD,YAAA,MAAM,EAAE,KAAK,CAAC,WAAW,CACvB,MACE,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC;AAC5B,gBAAA,MAAM,EAAE;oBACN,KAAK,EAAE,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC;AACrC,oBAAA,IAAI,EAAE,IAAyB;AAChC,iBAAA;gBACD,IAAI,EAAE,MAAM,CAAC,IAAI;AAClB,aAAA,CAAC,EACJ,CAAC,IAAI,EAAE,OAAO,CAAC,CAChB;YACD,GAAG,EAAE,KAAK,CAAC,WAAW,CACpB,CAAC,GAAG,KAAI;gBACN,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAEzC,gBAAA,IAAI,KAAK,IAAI,GAAG,EAAE;AAChB,oBAAA,KAAK,CAAC,EAAE,CAAC,GAAG,GAAG;AACb,wBAAA,KAAK,EAAE,MAAM,GAAG,CAAC,KAAK,EAAE;AACxB,wBAAA,MAAM,EAAE,MAAM,GAAG,CAAC,MAAM,EAAE;wBAC1B,iBAAiB,EAAE,CAAC,OAAe,KACjC,GAAG,CAAC,iBAAiB,CAAC,OAAO,CAAC;AAChC,wBAAA,cAAc,EAAE,MAAM,GAAG,CAAC,cAAc,EAAE;qBAC3C,CAAC;iBACH;aACF,EACD,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CACxB;AACF,SAAA;QACD,SAAS;AACT,QAAA,UAAU,EAAE,MAAM,CAAC,gBAAgB,CACjC,EAAE,EACF;AACE,YAAA,OAAO,EAAE;AACP,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC;AACzC,aAAA;AACD,YAAA,OAAO,EAAE;AACP,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC;AAC9C,aAAA;AACD,YAAA,SAAS,EAAE;AACT,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC;AAChD,aAAA;AACD,YAAA,YAAY,EAAE;AACZ,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC;AACnD,aAAA;AACD,YAAA,KAAK,EAAE;AACL,gBAAA,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAE,MAAM,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC;AACvC,aAAA;SACF,CACsB;KAC1B,CAAC;AACJ;;ACnMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCG;AACH,MAAM,UAAU,GAAG,CAIjB,KAA2C,KACxC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAsB,KAAK,CAAC;;AC9CpD,MAAM,OAAO,GAAG,CAAC,GAAgB,KAAI;IAC1C,MAAM,MAAM,GAAgB,EAAE,CAAC;IAE/B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAClC,IAAI,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;YAC1B,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAEjC,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AAC3C,gBAAA,MAAM,CAAC,CAAA,EAAG,GAAG,CAAA,CAAA,EAAI,SAAS,CAAA,CAAE,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;aACnD;SACF;aAAM;YACL,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;SACxB;KACF;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;;ACdD,MAAM,YAAY,GAAG,MAAM,CAAC;AAE5B;;;;;;;;;;;;;;;;;;;;;AAqBG;AACH,SAAS,IAAI,CAGX,KAAsB,EAAA;AACtB,IAAA,MAAM,OAAO,GAAG,cAAc,EAAK,CAAC;AACpC,IAAA,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACpD,IAAA,MAAM,EACJ,OAAO,GAAG,OAAO,CAAC,OAAO,EACzB,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,GAAG,YAAY,EACrB,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,SAAS,EACT,cAAc,EACd,GAAG,IAAI,EACR,GAAG,KAAK,CAAC;AAEV,IAAA,MAAM,MAAM,GAAG,OAAO,KAAgC,KAAI;QACxD,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,MAAM,OAAO,CAAC,YAAY,CAAC,OAAO,IAAI,KAAI;AACxC,YAAA,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;YAChC,IAAI,YAAY,GAAG,EAAE,CAAC;AAEtB,YAAA,IAAI;AACF,gBAAA,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;aACrC;YAAC,OAAM,EAAA,EAAA,GAAE;YAEV,MAAM,iBAAiB,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAEvD,YAAA,KAAK,MAAM,GAAG,IAAI,iBAAiB,EAAE;gBACnC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC;aAC9C;YAED,IAAI,QAAQ,EAAE;AACZ,gBAAA,MAAM,QAAQ,CAAC;oBACb,IAAI;oBACJ,KAAK;oBACL,MAAM;oBACN,QAAQ;oBACR,YAAY;AACb,iBAAA,CAAC,CAAC;aACJ;YAED,IAAI,MAAM,EAAE;AACV,gBAAA,IAAI;AACF,oBAAA,MAAM,6BAA6B,GAAG;AACpC,wBAAA,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC;wBAClC,OAAO;AACR,qBAAA,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAEnD,oBAAA,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,MAAM,EAAE;wBACnC,MAAM;AACN,wBAAA,OAAO,EAAE;AACP,4BAAA,GAAG,OAAO;AACV,4BAAA,IAAI,OAAO,GAAG,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;AAChD,yBAAA;wBACD,IAAI,EAAE,6BAA6B,GAAG,YAAY,GAAG,QAAQ;AAC9D,qBAAA,CAAC,CAAC;AAEH,oBAAA,IACE,QAAQ;AACR,yBAAC,cAAc;AACb,8BAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC;AAClC,8BAAE,QAAQ,CAAC,MAAM,GAAG,GAAG,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,CAAC,EACpD;wBACA,QAAQ,GAAG,IAAI,CAAC;AAChB,wBAAA,OAAO,IAAI,OAAO,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;AACjC,wBAAA,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;qBAChC;yBAAM;AACL,wBAAA,SAAS,IAAI,SAAS,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;qBACtC;iBACF;gBAAC,OAAO,KAAc,EAAE;oBACvB,QAAQ,GAAG,IAAI,CAAC;AAChB,oBAAA,OAAO,IAAI,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;iBAC/B;aACF;AACH,SAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAEV,QAAA,IAAI,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE;YAC7B,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACjC,gBAAA,kBAAkB,EAAE,KAAK;AAC1B,aAAA,CAAC,CAAC;AACH,YAAA,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE;gBACpC,IAAI;AACL,aAAA,CAAC,CAAC;SACJ;AACH,KAAC,CAAC;AAEF,IAAA,KAAK,CAAC,SAAS,CAAC,MAAK;QACnB,UAAU,CAAC,IAAI,CAAC,CAAC;KAClB,EAAE,EAAE,CAAC,CAAC;AAEP,IAAA,OAAO,MAAM,IACX,KAAA,CAAA,aAAA,CAAA,KAAA,CAAA,QAAA,EAAA,IAAA,EACG,MAAM,CAAC;QACN,MAAM;AACP,KAAA,CAAC,CACD,KAEH,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA,EACE,UAAU,EAAE,OAAO,EACnB,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,OAAO,EAAE,OAAO,EAChB,QAAQ,EAAE,MAAM,EAAA,GACZ,IAAI,EAAA,EAEP,QAAQ,CACJ,CACR,CAAC;AACJ;;AC5IA,mBAAe,CACb,IAAuB,EACvB,wBAAiC,EACjC,MAA2B,EAC3B,IAAY,EACZ,OAAuB,KAEvB,wBAAwB;AACtB,MAAE;QACE,GAAG,MAAM,CAAC,IAAI,CAAC;AACf,QAAA,KAAK,EAAE;YACL,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAE,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAE,CAAC,KAAK,GAAG,EAAE,CAAC;AACnE,YAAA,CAAC,IAAI,GAAG,OAAO,IAAI,IAAI;AACxB,SAAA;AACF,KAAA;MACD,EAAE;;ACrBR,iBAAe,MAAK;IAClB,MAAM,CAAC,GACL,OAAO,WAAW,KAAK,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;IAE7E,OAAO,sCAAsC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,KAAI;AACnE,QAAA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAE5C,OAAO,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;AACvD,KAAC,CAAC,CAAC;AACL,CAAC;;ACND,wBAAe,CACb,IAAuB,EACvB,KAAa,EACb,OAAA,GAAiC,EAAE,KAEnC,OAAO,CAAC,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;MACnD,OAAO,CAAC,SAAS;AACjB,QAAA,CAAA,EAAG,IAAI,CAAI,CAAA,EAAA,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,UAAU,CAAG,CAAA,CAAA;MAC1E,EAAE;;ACRR,yBAAe,CAAC,IAAW,MAA2B;IACpD,UAAU,EAAE,CAAC,IAAI,IAAI,IAAI,KAAK,eAAe,CAAC,QAAQ;AACtD,IAAA,QAAQ,EAAE,IAAI,KAAK,eAAe,CAAC,MAAM;AACzC,IAAA,UAAU,EAAE,IAAI,KAAK,eAAe,CAAC,QAAQ;AAC7C,IAAA,OAAO,EAAE,IAAI,KAAK,eAAe,CAAC,GAAG;AACrC,IAAA,SAAS,EAAE,IAAI,KAAK,eAAe,CAAC,SAAS;AAC9C,CAAA,CAAC;;ACPF,gBAAe,CACb,IAAuB,EACvB,MAAa,EACb,WAAqB,KAErB,CAAC,WAAW;KACX,MAAM,CAAC,QAAQ;AACd,QAAA,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;AACtB,QAAA,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CACpB,CAAC,SAAS,KACR,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;AAC1B,YAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAC9C,CAAC;;ACVN,MAAM,qBAAqB,GAAG,CAC5B,MAAiB,EACjB,MAAwD,EACxD,WAA8D,EAC9D,UAAoB,KAClB;AACF,IAAA,KAAK,MAAM,GAAG,IAAI,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QACpD,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAE/B,IAAI,KAAK,EAAE;YACT,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,EAAE,GAAG,KAAK,CAAC;YAEtC,IAAI,EAAE,EAAE;gBACN,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE;AACnE,oBAAA,OAAO,IAAI,CAAC;iBACb;AAAM,qBAAA,IAAI,EAAE,CAAC,GAAG,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC3D,oBAAA,OAAO,IAAI,CAAC;iBACb;qBAAM;AACL,oBAAA,IAAI,qBAAqB,CAAC,YAAY,EAAE,MAAM,CAAC,EAAE;wBAC/C,MAAM;qBACP;iBACF;aACF;AAAM,iBAAA,IAAI,QAAQ,CAAC,YAAY,CAAC,EAAE;AACjC,gBAAA,IAAI,qBAAqB,CAAC,YAAyB,EAAE,MAAM,CAAC,EAAE;oBAC5D,MAAM;iBACP;aACF;SACF;KACF;IACD,OAAO;AACT,CAAC;;ACxBD,gCAAe,CACb,MAAsB,EACtB,KAA0C,EAC1C,IAAuB,KACL;IAClB,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAClE,GAAG,CAAC,gBAAgB,EAAE,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3C,IAAA,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;AACpC,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;;ACjBD,kBAAe,CAAC,OAAqB,KACnC,OAAO,CAAC,IAAI,KAAK,MAAM;;ACHzB,iBAAe,CAAC,KAAc,KAC5B,OAAO,KAAK,KAAK,UAAU;;ACC7B,oBAAe,CAAC,KAAc,KAA0B;IACtD,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,MAAM,KAAK,GAAG,KAAK,GAAK,KAAqB,CAAC,aAA0B,GAAG,CAAC,CAAC;AAC7E,IAAA,QACE,KAAK;AACL,SAAC,KAAK,IAAI,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,WAAW,GAAG,WAAW,CAAC,EAC1E;AACJ,CAAC;;ACTD,gBAAe,CAAC,KAAc,KAAuB,QAAQ,CAAC,KAAK,CAAC;;ACDpE,mBAAe,CAAC,OAAqB,KACnC,OAAO,CAAC,IAAI,KAAK,OAAO;;ACH1B,cAAe,CAAC,KAAc,KAAsB,KAAK,YAAY,MAAM;;ACO3E,MAAM,aAAa,GAAwB;AACzC,IAAA,KAAK,EAAE,KAAK;AACZ,IAAA,OAAO,EAAE,KAAK;CACf,CAAC;AAEF,MAAM,WAAW,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAEnD,uBAAe,CAAC,OAA4B,KAAyB;AACnE,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AAC1B,QAAA,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACtB,MAAM,MAAM,GAAG,OAAO;AACnB,iBAAA,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;iBAChE,GAAG,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC;AACjC,YAAA,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;SACpD;AAED,QAAA,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ;AAC/C;AACE,gBAAA,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC;AAClE,sBAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;AACxD,0BAAE,WAAW;AACb,0BAAE,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE;AAC9C,sBAAE,WAAW;cACb,aAAa,CAAC;KACnB;AAED,IAAA,OAAO,aAAa,CAAC;AACvB,CAAC;;AC7BD,MAAM,aAAa,GAAqB;AACtC,IAAA,OAAO,EAAE,KAAK;AACd,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF,oBAAe,CAAC,OAA4B,KAC1C,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;MAClB,OAAO,CAAC,MAAM,CACZ,CAAC,QAAQ,EAAE,MAAM,KACf,MAAM,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ;AAC1C,UAAE;AACE,YAAA,OAAO,EAAE,IAAI;YACb,KAAK,EAAE,MAAM,CAAC,KAAK;AACpB,SAAA;AACH,UAAE,QAAQ,EACd,aAAa,CACd;MACD,aAAa;;AClBL,SAAU,gBAAgB,CACtC,MAAsB,EACtB,GAAQ,EACR,IAAI,GAAG,UAAU,EAAA;IAEjB,IACE,SAAS,CAAC,MAAM,CAAC;AACjB,SAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;SACjD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAC9B;QACA,OAAO;YACL,IAAI;AACJ,YAAA,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,EAAE;YACxC,GAAG;SACJ,CAAC;KACH;AACH;;AChBA,yBAAe,CAAC,cAA+B,KAC7C,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;AAClD,MAAE,cAAc;AAChB,MAAE;AACE,QAAA,KAAK,EAAE,cAAc;AACrB,QAAA,OAAO,EAAE,EAAE;KACZ;;ACsBP,oBAAe,OACb,KAAY,EACZ,UAAa,EACb,wBAAiC,EACjC,yBAAmC,EACnC,YAAsB,KACU;AAChC,IAAA,MAAM,EACJ,GAAG,EACH,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,SAAS,EACT,GAAG,EACH,GAAG,EACH,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,aAAa,EACb,KAAK,EACL,QAAQ,GACT,GAAG,KAAK,CAAC,EAAE,CAAC;IACb,MAAM,UAAU,GAAqB,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AAC3D,IAAA,IAAI,CAAC,KAAK,IAAI,QAAQ,EAAE;AACtB,QAAA,OAAO,EAAE,CAAC;KACX;AACD,IAAA,MAAM,QAAQ,GAAqB,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,GAAI,GAAwB,CAAC;AAC9E,IAAA,MAAM,iBAAiB,GAAG,CAAC,OAA0B,KAAI;AACvD,QAAA,IAAI,yBAAyB,IAAI,QAAQ,CAAC,cAAc,EAAE;AACxD,YAAA,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC,CAAC;YACpE,QAAQ,CAAC,cAAc,EAAE,CAAC;SAC3B;AACH,KAAC,CAAC;IACF,MAAM,KAAK,GAAwB,EAAE,CAAC;AACtC,IAAA,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;AAClC,IAAA,MAAM,UAAU,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;AACxC,IAAA,MAAM,iBAAiB,GAAG,OAAO,IAAI,UAAU,CAAC;IAChD,MAAM,OAAO,GACX,CAAC,CAAC,aAAa,IAAI,WAAW,CAAC,GAAG,CAAC;AACjC,QAAA,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC;QACtB,WAAW,CAAC,UAAU,CAAC;SACxB,aAAa,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,KAAK,EAAE,CAAC;AACxC,QAAA,UAAU,KAAK,EAAE;AACjB,SAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACpD,IAAA,MAAM,iBAAiB,GAAG,YAAY,CAAC,IAAI,CACzC,IAAI,EACJ,IAAI,EACJ,wBAAwB,EACxB,KAAK,CACN,CAAC;AACF,IAAA,MAAM,gBAAgB,GAAG,CACvB,SAAkB,EAClB,gBAAyB,EACzB,gBAAyB,EACzB,UAAmB,sBAAsB,CAAC,SAAS,EACnD,OAAA,GAAmB,sBAAsB,CAAC,SAAS,KACjD;QACF,MAAM,OAAO,GAAG,SAAS,GAAG,gBAAgB,GAAG,gBAAgB,CAAC;QAChE,KAAK,CAAC,IAAI,CAAC,GAAG;YACZ,IAAI,EAAE,SAAS,GAAG,OAAO,GAAG,OAAO;YACnC,OAAO;YACP,GAAG;AACH,YAAA,GAAG,iBAAiB,CAAC,SAAS,GAAG,OAAO,GAAG,OAAO,EAAE,OAAO,CAAC;SAC7D,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,IACE,YAAY;AACV,UAAE,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM;AAClD,UAAE,QAAQ;AACR,aAAC,CAAC,CAAC,iBAAiB,KAAK,OAAO,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;AAChE,iBAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;iBACrC,UAAU,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;AAC/C,iBAAC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,EAChD;QACA,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,SAAS,CAAC,QAAQ,CAAC;cAC1C,EAAE,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE;AAC1C,cAAE,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAEjC,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,IAAI,CAAC,GAAG;gBACZ,IAAI,EAAE,sBAAsB,CAAC,QAAQ;gBACrC,OAAO;AACP,gBAAA,GAAG,EAAE,QAAQ;AACb,gBAAA,GAAG,iBAAiB,CAAC,sBAAsB,CAAC,QAAQ,EAAE,OAAO,CAAC;aAC/D,CAAC;YACF,IAAI,CAAC,wBAAwB,EAAE;gBAC7B,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAC3B,gBAAA,OAAO,KAAK,CAAC;aACd;SACF;KACF;AAED,IAAA,IAAI,CAAC,OAAO,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE;AACpE,QAAA,IAAI,SAAS,CAAC;AACd,QAAA,IAAI,SAAS,CAAC;AACd,QAAA,MAAM,SAAS,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC1C,QAAA,MAAM,SAAS,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAE1C,QAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,UAAoB,CAAC,EAAE;AAClE,YAAA,MAAM,WAAW,GACd,GAAwB,CAAC,aAAa;iBACtC,UAAU,GAAG,CAAC,UAAU,GAAG,UAAU,CAAC,CAAC;YAC1C,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;AACvC,gBAAA,SAAS,GAAG,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC;aAC3C;YACD,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;AACvC,gBAAA,SAAS,GAAG,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC;aAC3C;SACF;aAAM;YACL,MAAM,SAAS,GACZ,GAAwB,CAAC,WAAW,IAAI,IAAI,IAAI,CAAC,UAAoB,CAAC,CAAC;YAC1E,MAAM,iBAAiB,GAAG,CAAC,IAAa,KACtC,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,YAAY,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;AACnD,YAAA,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC;AAClC,YAAA,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC;YAElC,IAAI,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,UAAU,EAAE;AAC3C,gBAAA,SAAS,GAAG,MAAM;sBACd,iBAAiB,CAAC,UAAU,CAAC,GAAG,iBAAiB,CAAC,SAAS,CAAC,KAAK,CAAC;AACpE,sBAAE,MAAM;AACN,0BAAE,UAAU,GAAG,SAAS,CAAC,KAAK;0BAC5B,SAAS,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aAC7C;YAED,IAAI,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,UAAU,EAAE;AAC3C,gBAAA,SAAS,GAAG,MAAM;sBACd,iBAAiB,CAAC,UAAU,CAAC,GAAG,iBAAiB,CAAC,SAAS,CAAC,KAAK,CAAC;AACpE,sBAAE,MAAM;AACN,0BAAE,UAAU,GAAG,SAAS,CAAC,KAAK;0BAC5B,SAAS,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aAC7C;SACF;AAED,QAAA,IAAI,SAAS,IAAI,SAAS,EAAE;YAC1B,gBAAgB,CACd,CAAC,CAAC,SAAS,EACX,SAAS,CAAC,OAAO,EACjB,SAAS,CAAC,OAAO,EACjB,sBAAsB,CAAC,GAAG,EAC1B,sBAAsB,CAAC,GAAG,CAC3B,CAAC;YACF,IAAI,CAAC,wBAAwB,EAAE;gBAC7B,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAE,CAAC,OAAO,CAAC,CAAC;AACxC,gBAAA,OAAO,KAAK,CAAC;aACd;SACF;KACF;AAED,IAAA,IACE,CAAC,SAAS,IAAI,SAAS;AACvB,QAAA,CAAC,OAAO;AACR,SAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,EACrE;AACA,QAAA,MAAM,eAAe,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;AACtD,QAAA,MAAM,eAAe,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;QACtD,MAAM,SAAS,GACb,CAAC,iBAAiB,CAAC,eAAe,CAAC,KAAK,CAAC;AACzC,YAAA,UAAU,CAAC,MAAM,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC;QAC7C,MAAM,SAAS,GACb,CAAC,iBAAiB,CAAC,eAAe,CAAC,KAAK,CAAC;AACzC,YAAA,UAAU,CAAC,MAAM,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC;AAE7C,QAAA,IAAI,SAAS,IAAI,SAAS,EAAE;YAC1B,gBAAgB,CACd,SAAS,EACT,eAAe,CAAC,OAAO,EACvB,eAAe,CAAC,OAAO,CACxB,CAAC;YACF,IAAI,CAAC,wBAAwB,EAAE;gBAC7B,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAE,CAAC,OAAO,CAAC,CAAC;AACxC,gBAAA,OAAO,KAAK,CAAC;aACd;SACF;KACF;IAED,IAAI,OAAO,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE;AAC/C,QAAA,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;AAErE,QAAA,IAAI,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;YAC5D,KAAK,CAAC,IAAI,CAAC,GAAG;gBACZ,IAAI,EAAE,sBAAsB,CAAC,OAAO;gBACpC,OAAO;gBACP,GAAG;AACH,gBAAA,GAAG,iBAAiB,CAAC,sBAAsB,CAAC,OAAO,EAAE,OAAO,CAAC;aAC9D,CAAC;YACF,IAAI,CAAC,wBAAwB,EAAE;gBAC7B,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAC3B,gBAAA,OAAO,KAAK,CAAC;aACd;SACF;KACF;IAED,IAAI,QAAQ,EAAE;AACZ,QAAA,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;YACxB,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YACtD,MAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAEzD,IAAI,aAAa,EAAE;gBACjB,KAAK,CAAC,IAAI,CAAC,GAAG;AACZ,oBAAA,GAAG,aAAa;oBAChB,GAAG,iBAAiB,CAClB,sBAAsB,CAAC,QAAQ,EAC/B,aAAa,CAAC,OAAO,CACtB;iBACF,CAAC;gBACF,IAAI,CAAC,wBAAwB,EAAE;AAC7B,oBAAA,iBAAiB,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACzC,oBAAA,OAAO,KAAK,CAAC;iBACd;aACF;SACF;AAAM,aAAA,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;YAC7B,IAAI,gBAAgB,GAAG,EAAgB,CAAC;AAExC,YAAA,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE;gBAC1B,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,wBAAwB,EAAE;oBACjE,MAAM;iBACP;AAED,gBAAA,MAAM,aAAa,GAAG,gBAAgB,CACpC,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,EAC3C,QAAQ,EACR,GAAG,CACJ,CAAC;gBAEF,IAAI,aAAa,EAAE;AACjB,oBAAA,gBAAgB,GAAG;AACjB,wBAAA,GAAG,aAAa;AAChB,wBAAA,GAAG,iBAAiB,CAAC,GAAG,EAAE,aAAa,CAAC,OAAO,CAAC;qBACjD,CAAC;AAEF,oBAAA,iBAAiB,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;oBAEzC,IAAI,wBAAwB,EAAE;AAC5B,wBAAA,KAAK,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC;qBAChC;iBACF;aACF;AAED,YAAA,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE;gBACpC,KAAK,CAAC,IAAI,CAAC,GAAG;AACZ,oBAAA,GAAG,EAAE,QAAQ;AACb,oBAAA,GAAG,gBAAgB;iBACpB,CAAC;gBACF,IAAI,CAAC,wBAAwB,EAAE;AAC7B,oBAAA,OAAO,KAAK,CAAC;iBACd;aACF;SACF;KACF;IAED,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACxB,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;;AC3RD,eAAe,CAAI,IAAS,EAAE,KAAc,KAAU;AACpD,IAAA,GAAG,IAAI;IACP,GAAG,qBAAqB,CAAC,KAAK,CAAC;CAChC;;ACLD,qBAAe,CAAI,KAAc,KAC/B,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,SAAS,CAAC,GAAG,SAAS;;ACOvC,SAAA,MAAM,CAC5B,IAAS,EACT,KAAa,EACb,KAAe,EAAA;IAEf,OAAO;AACL,QAAA,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;QACvB,GAAG,qBAAqB,CAAC,KAAK,CAAC;AAC/B,QAAA,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;KACrB,CAAC;AACJ;;AChBA,kBAAe,CACb,IAAuB,EACvB,IAAY,EACZ,EAAU,KACW;IACrB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACxB,QAAA,OAAO,EAAE,CAAC;KACX;IAED,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;AACzB,QAAA,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC;KACtB;AACD,IAAA,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAE5C,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;;ACfD,gBAAe,CAAI,IAAS,EAAE,KAAc,KAAU;IACpD,GAAG,qBAAqB,CAAC,KAAK,CAAC;IAC/B,GAAG,qBAAqB,CAAC,IAAI,CAAC;CAC/B;;ACDD,SAAS,eAAe,CAAI,IAAS,EAAE,OAAiB,EAAA;IACtD,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,IAAA,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;AAEvB,IAAA,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;QAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1B,QAAA,CAAC,EAAE,CAAC;KACL;AAED,IAAA,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;AAC1C,CAAC;AAED,oBAAe,CAAI,IAAS,EAAE,KAAyB,KACrD,WAAW,CAAC,KAAK,CAAC;AAChB,MAAE,EAAE;MACF,eAAe,CACb,IAAI,EACH,qBAAqB,CAAC,KAAK,CAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CACjE;;ACtBP,kBAAe,CAAI,IAAS,EAAE,MAAc,EAAE,MAAc,KAAU;IACpE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9D,CAAC;;ACID,SAAS,OAAO,CAAC,MAAW,EAAE,UAA+B,EAAA;AAC3D,IAAA,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAC9C,IAAI,KAAK,GAAG,CAAC,CAAC;AAEd,IAAA,OAAO,KAAK,GAAG,MAAM,EAAE;QACrB,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,KAAK,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;KACtE;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,YAAY,CAAC,GAAc,EAAA;AAClC,IAAA,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;AACrB,QAAA,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AACrD,YAAA,OAAO,KAAK,CAAC;SACd;KACF;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAEa,SAAU,KAAK,CAAC,MAAW,EAAE,IAAkC,EAAA;AAC3E,IAAA,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAC/B,UAAE,IAAI;AACN,UAAE,KAAK,CAAC,IAAI,CAAC;cACT,CAAC,IAAI,CAAC;AACR,cAAE,YAAY,CAAC,IAAI,CAAC,CAAC;IAEzB,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAEzE,IAAA,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC/B,IAAA,MAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;IAEzB,IAAI,WAAW,EAAE;AACf,QAAA,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC;KACzB;IAED,IACE,KAAK,KAAK,CAAC;SACV,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,aAAa,CAAC,WAAW,CAAC;AACnD,aAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,EAC5D;AACA,QAAA,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KACnC;AAED,IAAA,OAAO,MAAM,CAAC;AAChB;;ACnDA,eAAe,CAAI,WAAgB,EAAE,KAAa,EAAE,KAAQ,KAAI;AAC9D,IAAA,WAAW,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AAC3B,IAAA,OAAO,WAAW,CAAC;AACrB,CAAC;;ACwCD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCG;AACG,SAAU,aAAa,CAM3B,KAAkE,EAAA;AAElE,IAAA,MAAM,OAAO,GAAG,cAAc,EAAE,CAAC;AACjC,IAAA,MAAM,EACJ,OAAO,GAAG,OAAO,CAAC,OAAO,EACzB,IAAI,EACJ,OAAO,GAAG,IAAI,EACd,gBAAgB,GACjB,GAAG,KAAK,CAAC;AACV,IAAA,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;AACzE,IAAA,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CACtB,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAC7C,CAAC;IACF,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACvC,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACjC,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAEtC,IAAA,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;AACrB,IAAA,SAAS,CAAC,OAAO,GAAG,MAAM,CAAC;IAC3B,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAE/B,IAAA,KAAK,CAAC,KAAK;QACR,OAAiC,CAAC,QAAQ,CACzC,IAA+B,EAC/B,KAAK,CAAC,KAAsC,CAC7C,CAAC;AAEJ,IAAA,YAAY,CAAC;QACX,IAAI,EAAE,CAAC,EACL,MAAM,EACN,IAAI,EAAE,cAAc,GAIrB,KAAI;YACH,IAAI,cAAc,KAAK,KAAK,CAAC,OAAO,IAAI,CAAC,cAAc,EAAE;gBACvD,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AAC/C,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;oBAC9B,SAAS,CAAC,WAAW,CAAC,CAAC;oBACvB,GAAG,CAAC,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;iBAC3C;aACF;SACF;AACD,QAAA,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK;AACjC,KAAA,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CACpC,CAKE,uBAA0B,KACxB;AACF,QAAA,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC;AACzB,QAAA,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;AAC3D,KAAC,EACD,CAAC,OAAO,EAAE,IAAI,CAAC,CAChB,CAAC;AAEF,IAAA,MAAM,MAAM,GAAG,CACb,KAEwD,EACxD,OAA+B,KAC7B;QACF,MAAM,WAAW,GAAG,qBAAqB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9D,QAAA,MAAM,uBAAuB,GAAG,QAAQ,CACtC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAC5B,WAAW,CACZ,CAAC;AACF,QAAA,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,iBAAiB,CACtC,IAAI,EACJ,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAClC,OAAO,CACR,CAAC;AACF,QAAA,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;QACjE,YAAY,CAAC,uBAAuB,CAAC,CAAC;QACtC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QACnC,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,uBAAuB,EAAE,QAAQ,EAAE;AACjE,YAAA,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC5B,SAAA,CAAC,CAAC;AACL,KAAC,CAAC;AAEF,IAAA,MAAM,OAAO,GAAG,CACd,KAEwD,EACxD,OAA+B,KAC7B;QACF,MAAM,YAAY,GAAG,qBAAqB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/D,QAAA,MAAM,uBAAuB,GAAG,SAAS,CACvC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAC5B,YAAY,CACb,CAAC;AACF,QAAA,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,iBAAiB,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;AAC3D,QAAA,GAAG,CAAC,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;QACnE,YAAY,CAAC,uBAAuB,CAAC,CAAC;QACtC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QACnC,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,uBAAuB,EAAE,SAAS,EAAE;AAClE,YAAA,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC5B,SAAA,CAAC,CAAC;AACL,KAAC,CAAC;AAEF,IAAA,MAAM,MAAM,GAAG,CAAC,KAAyB,KAAI;AAC3C,QAAA,MAAM,uBAAuB,GAEvB,aAAa,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;QACzD,GAAG,CAAC,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAChD,YAAY,CAAC,uBAAuB,CAAC,CAAC;QACtC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QACnC,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,uBAAuB,EAAE,aAAa,EAAE;AACtE,YAAA,IAAI,EAAE,KAAK;AACZ,SAAA,CAAC,CAAC;AACL,KAAC,CAAC;IAEF,MAAMA,QAAM,GAAG,CACb,KAAa,EACb,KAEwD,EACxD,OAA+B,KAC7B;QACF,MAAM,WAAW,GAAG,qBAAqB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9D,QAAA,MAAM,uBAAuB,GAAGC,MAAQ,CACtC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAC5B,KAAK,EACL,WAAW,CACZ,CAAC;AACF,QAAA,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC/D,QAAA,GAAG,CAAC,OAAO,GAAGA,MAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;QACxE,YAAY,CAAC,uBAAuB,CAAC,CAAC;QACtC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QACnC,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,uBAAuB,EAAEA,MAAQ,EAAE;AACjE,YAAA,IAAI,EAAE,KAAK;AACX,YAAA,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC5B,SAAA,CAAC,CAAC;AACL,KAAC,CAAC;AAEF,IAAA,MAAM,IAAI,GAAG,CAAC,MAAc,EAAE,MAAc,KAAI;QAC9C,MAAM,uBAAuB,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAC7D,QAAA,WAAW,CAAC,uBAAuB,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QACrD,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QACzC,YAAY,CAAC,uBAAuB,CAAC,CAAC;QACtC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QACnC,OAAO,CAAC,iBAAiB,CACvB,IAAI,EACJ,uBAAuB,EACvB,WAAW,EACX;AACE,YAAA,IAAI,EAAE,MAAM;AACZ,YAAA,IAAI,EAAE,MAAM;SACb,EACD,KAAK,CACN,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,MAAM,IAAI,GAAG,CAAC,IAAY,EAAE,EAAU,KAAI;QACxC,MAAM,uBAAuB,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAC7D,QAAA,WAAW,CAAC,uBAAuB,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QAC/C,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QACnC,YAAY,CAAC,uBAAuB,CAAC,CAAC;QACtC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QACnC,OAAO,CAAC,iBAAiB,CACvB,IAAI,EACJ,uBAAuB,EACvB,WAAW,EACX;AACE,YAAA,IAAI,EAAE,IAAI;AACV,YAAA,IAAI,EAAE,EAAE;SACT,EACD,KAAK,CACN,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,MAAM,MAAM,GAAG,CACb,KAAa,EACb,KAAgD,KAC9C;AACF,QAAA,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AACvC,QAAA,MAAM,uBAAuB,GAAG,QAAQ,CACtC,OAAO,CAAC,cAAc,CAEpB,IAAI,CAAC,EACP,KAAK,EACL,WAAwE,CACzE,CAAC;AACF,QAAA,GAAG,CAAC,OAAO,GAAG,CAAC,GAAG,uBAAuB,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,KACrD,CAAC,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,UAAU,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CACrD,CAAC;QACF,YAAY,CAAC,uBAAuB,CAAC,CAAC;AACtC,QAAA,SAAS,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC;QACxC,OAAO,CAAC,iBAAiB,CACvB,IAAI,EACJ,uBAAuB,EACvB,QAAQ,EACR;AACE,YAAA,IAAI,EAAE,KAAK;AACX,YAAA,IAAI,EAAE,WAAW;AAClB,SAAA,EACD,IAAI,EACJ,KAAK,CACN,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,MAAM,OAAO,GAAG,CACd,KAEwD,KACtD;QACF,MAAM,uBAAuB,GAAG,qBAAqB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;QAC1E,GAAG,CAAC,OAAO,GAAG,uBAAuB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACtD,QAAA,YAAY,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC;AAC3C,QAAA,SAAS,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC;QACxC,OAAO,CAAC,iBAAiB,CACvB,IAAI,EACJ,CAAC,GAAG,uBAAuB,CAAC,EAC5B,CAAI,IAAO,KAAQ,IAAI,EACvB,EAAE,EACF,IAAI,EACJ,KAAK,CACN,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,KAAK,CAAC,SAAS,CAAC,MAAK;AACnB,QAAA,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC;AAE9B,QAAA,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC;AAC7B,YAAA,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC3B,GAAG,OAAO,CAAC,UAAU;AACK,aAAA,CAAC,CAAC;QAEhC,IACE,SAAS,CAAC,OAAO;aAChB,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,UAAU;AACpD,gBAAA,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,EACjC;AACA,YAAA,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE;AAC7B,gBAAA,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAI;oBAC7C,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACvC,oBAAA,MAAM,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAE3D,oBAAA,IACE,aAAa;0BACT,CAAC,CAAC,KAAK,IAAI,aAAa,CAAC,IAAI;AAC7B,6BAAC,KAAK;AACJ,iCAAC,aAAa,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;AAChC,oCAAA,aAAa,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;AAC/C,0BAAE,KAAK,IAAI,KAAK,CAAC,IAAI,EACvB;wBACA,KAAK;AACH,8BAAE,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC;8BAC3C,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC3C,wBAAA,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AAC3B,4BAAA,MAAM,EAAE,OAAO,CAAC,UAAU,CAAC,MAAmC;AAC/D,yBAAA,CAAC,CAAC;qBACJ;AACH,iBAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,MAAM,KAAK,GAAU,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAChD,gBAAA,IACE,KAAK;AACL,oBAAA,KAAK,CAAC,EAAE;oBACR,EACE,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,UAAU;wBAC9D,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,UAAU,CACrD,EACD;AACA,oBAAA,aAAa,CACX,KAAK,EACL,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,QAAQ,CAAC,YAAY,KAAK,eAAe,CAAC,GAAG,EACrD,OAAO,CAAC,QAAQ,CAAC,yBAAyB,EAC1C,IAAI,CACL,CAAC,IAAI,CACJ,CAAC,KAAK,KACJ,CAAC,aAAa,CAAC,KAAK,CAAC;AACrB,wBAAA,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AAC3B,4BAAA,MAAM,EAAE,yBAAyB,CAC/B,OAAO,CAAC,UAAU,CAAC,MAAmC,EACtD,KAAK,EACL,IAAI,CACwB;AAC/B,yBAAA,CAAC,CACL,CAAC;iBACH;aACF;SACF;AAED,QAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;YAC5B,IAAI;AACJ,YAAA,MAAM,EAAE,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE;AACnC,SAAA,CAAC,CAAC;QAEH,OAAO,CAAC,MAAM,CAAC,KAAK;YAClB,qBAAqB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAW,KAAI;AAC1D,gBAAA,IACE,OAAO,CAAC,MAAM,CAAC,KAAK;oBACpB,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;oBACpC,GAAG,CAAC,KAAK,EACT;oBACA,GAAG,CAAC,KAAK,EAAE,CAAC;AACZ,oBAAA,OAAO,CAAC,CAAC;iBACV;gBACD,OAAO;AACT,aAAC,CAAC,CAAC;AAEL,QAAA,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE,CAAC;QAE1B,OAAO,CAAC,YAAY,EAAE,CAAC;AACvB,QAAA,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC;KAC3B,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;AAE5B,IAAA,KAAK,CAAC,SAAS,CAAC,MAAK;AACnB,QAAA,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAEnE,QAAA,OAAO,MAAK;AACV,YAAA,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,IAAI,gBAAgB;AACpD,gBAAA,OAAO,CAAC,UAAU,CAAC,IAA+B,CAAC,CAAC;AACxD,SAAC,CAAC;KACH,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAE/C,OAAO;AACL,QAAA,IAAI,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC5D,QAAA,IAAI,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC5D,QAAA,OAAO,EAAE,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAClE,QAAA,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAChE,QAAA,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAChE,QAAA,MAAM,EAAE,KAAK,CAAC,WAAW,CAACD,QAAM,EAAE,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAChE,QAAA,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAChE,QAAA,OAAO,EAAE,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAClE,QAAA,MAAM,EAAE,KAAK,CAAC,OAAO,CACnB,MACE,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,MAAM;AAC5B,YAAA,GAAG,KAAK;YACR,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,UAAU,EAAE;AAC9C,SAAA,CAAC,CAAgE,EACpE,CAAC,MAAM,EAAE,OAAO,CAAC,CAClB;KACF,CAAC;AACJ;;AC1ZA,oBAAe,MAAoB;IACjC,IAAI,UAAU,GAAkB,EAAE,CAAC;AAEnC,IAAA,MAAM,IAAI,GAAG,CAAC,KAAQ,KAAI;AACxB,QAAA,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE;YACjC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACvC;AACH,KAAC,CAAC;AAEF,IAAA,MAAM,SAAS,GAAG,CAAC,QAAqB,KAAkB;AACxD,QAAA,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1B,OAAO;YACL,WAAW,EAAE,MAAK;AAChB,gBAAA,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,QAAQ,CAAC,CAAC;aACvD;SACF,CAAC;AACJ,KAAC,CAAC;IAEF,MAAM,WAAW,GAAG,MAAK;QACvB,UAAU,GAAG,EAAE,CAAC;AAClB,KAAC,CAAC;IAEF,OAAO;AACL,QAAA,IAAI,SAAS,GAAA;AACX,YAAA,OAAO,UAAU,CAAC;SACnB;QACD,IAAI;QACJ,SAAS;QACT,WAAW;KACZ,CAAC;AACJ,CAAC;;ACzCD,kBAAe,CAAC,KAAc,KAC5B,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;;ACDpC,SAAU,SAAS,CAAC,OAAY,EAAE,OAAY,EAAA;IAC1D,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;QAChD,OAAO,OAAO,KAAK,OAAO,CAAC;KAC5B;IAED,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,EAAE;QAClD,OAAO,OAAO,CAAC,OAAO,EAAE,KAAK,OAAO,CAAC,OAAO,EAAE,CAAC;KAChD;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACnC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAEnC,IAAI,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,EAAE;AACjC,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;AACvB,QAAA,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;QAE1B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACxB,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,IAAI,GAAG,KAAK,KAAK,EAAE;AACjB,YAAA,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YAE1B,IACE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC;iBACxC,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AAClC,iBAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC1C,kBAAE,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC;AACxB,kBAAE,IAAI,KAAK,IAAI,EACjB;AACA,gBAAA,OAAO,KAAK,CAAC;aACd;SACF;KACF;AAED,IAAA,OAAO,IAAI,CAAC;AACd;;AC1CA,uBAAe,CAAC,OAAqB,KACnC,OAAO,CAAC,IAAI,KAAK,CAAA,eAAA,CAAiB;;ACEpC,wBAAe,CAAC,GAAiB,KAC/B,YAAY,CAAC,GAAG,CAAC,IAAI,eAAe,CAAC,GAAG,CAAC;;ACF3C,WAAe,CAAC,GAAQ,KAAK,aAAa,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,WAAW;;ACFlE,wBAAe,CAAI,IAAO,KAAa;AACrC,IAAA,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;QACtB,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;AACzB,YAAA,OAAO,IAAI,CAAC;SACb;KACF;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;;ACFD,SAAS,eAAe,CAAI,IAAO,EAAE,SAA8B,EAAE,EAAA;IACnE,MAAM,iBAAiB,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAE9C,IAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE;AACvC,QAAA,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACtB,IACE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxB,iBAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EACtD;gBACA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;gBACjD,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;aACzC;iBAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;AACxC,gBAAA,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;aACpB;SACF;KACF;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,+BAA+B,CACtC,IAAO,EACP,UAAa,EACb,qBAGC,EAAA;IAED,MAAM,iBAAiB,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAE9C,IAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE;AACvC,QAAA,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACtB,IACE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxB,iBAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EACtD;gBACA,IACE,WAAW,CAAC,UAAU,CAAC;AACvB,oBAAA,WAAW,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,EACvC;AACA,oBAAA,qBAAqB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;0BACjD,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;0BAC9B,EAAE,GAAG,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;iBACvC;qBAAM;oBACL,+BAA+B,CAC7B,IAAI,CAAC,GAAG,CAAC,EACT,iBAAiB,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC,EACpD,qBAAqB,CAAC,GAAG,CAAC,CAC3B,CAAC;iBACH;aACF;iBAAM;AACL,gBAAA,qBAAqB,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;aACrE;SACF;KACF;AAED,IAAA,OAAO,qBAAqB,CAAC;AAC/B,CAAC;AAED,qBAAe,CAAI,aAAgB,EAAE,UAAa,KAChD,+BAA+B,CAC7B,aAAa,EACb,UAAU,EACV,eAAe,CAAC,UAAU,CAAC,CAC5B;;ACnEH,sBAAe,CACb,KAAQ,EACR,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAe,KAEvD,WAAW,CAAC,KAAK,CAAC;AAChB,MAAE,KAAK;AACP,MAAE,aAAa;UACX,KAAK,KAAK,EAAE;AACZ,cAAE,GAAG;AACL,cAAE,KAAK;kBACH,CAAC,KAAK;AACR,kBAAE,KAAK;AACX,UAAE,WAAW,IAAI,QAAQ,CAAC,KAAK,CAAC;AAC9B,cAAE,IAAI,IAAI,CAAC,KAAK,CAAC;AACjB,cAAE,UAAU;AACV,kBAAE,UAAU,CAAC,KAAK,CAAC;kBACjB,KAAK;;ACTO,SAAA,aAAa,CAAC,EAAe,EAAA;AACnD,IAAA,MAAM,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC;AAEnB,IAAA,IAAI,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,QAAQ,EAAE;QACjE,OAAO;KACR;AAED,IAAA,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;QACpB,OAAO,GAAG,CAAC,KAAK,CAAC;KAClB;AAED,IAAA,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;QACrB,OAAO,aAAa,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;KACrC;AAED,IAAA,IAAI,gBAAgB,CAAC,GAAG,CAAC,EAAE;AACzB,QAAA,OAAO,CAAC,GAAG,GAAG,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,CAAC;KAC3D;AAED,IAAA,IAAIE,eAAU,CAAC,GAAG,CAAC,EAAE;QACnB,OAAO,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;KACxC;IAED,OAAO,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAChF;;ACxBA,yBAAe,CACb,WAAyD,EACzD,OAAkB,EAClB,YAA2B,EAC3B,yBAA+C,KAC7C;IACF,MAAM,MAAM,GAA2C,EAAE,CAAC;AAE1D,IAAA,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE;QAC9B,MAAM,KAAK,GAAU,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAExC,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;KACtC;IAED,OAAO;QACL,YAAY;AACZ,QAAA,KAAK,EAAE,CAAC,GAAG,WAAW,CAA8B;QACpD,MAAM;QACN,yBAAyB;KAC1B,CAAC;AACJ,CAAC;;ACtBD,mBAAe,CACb,IAAoD,KAEpD,WAAW,CAAC,IAAI,CAAC;AACf,MAAE,IAAI;AACN,MAAE,OAAO,CAAC,IAAI,CAAC;UACX,IAAI,CAAC,MAAM;AACb,UAAE,QAAQ,CAAC,IAAI,CAAC;AACd,cAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;AACnB,kBAAE,IAAI,CAAC,KAAK,CAAC,MAAM;kBACjB,IAAI,CAAC,KAAK;cACZ,IAAI;;AChBd,MAAM,cAAc,GAAG,eAAe,CAAC;AAEvC,2BAAe,CAAC,cAA2B,KACzC,CAAC,CAAC,cAAc,IAAI,CAAC,cAAc,CAAC,QAAQ;IAC5C,CAAC,EACC,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC;QAClC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,KAAK,cAAc;AAC7D,SAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,CACzC,CAAC,gBAA4C,KAC3C,gBAAgB,CAAC,WAAW,CAAC,IAAI,KAAK,cAAc,CACvD,CAAC,CACL;;ACdH,oBAAe,CAAC,OAAoB,KAClC,OAAO,CAAC,KAAK;KACZ,OAAO,CAAC,QAAQ;AACf,QAAA,OAAO,CAAC,GAAG;AACX,QAAA,OAAO,CAAC,GAAG;AACX,QAAA,OAAO,CAAC,SAAS;AACjB,QAAA,OAAO,CAAC,SAAS;AACjB,QAAA,OAAO,CAAC,OAAO;QACf,OAAO,CAAC,QAAQ,CAAC;;ACNG,SAAA,iBAAiB,CACvC,MAAsB,EACtB,OAAoB,EACpB,IAAY,EAAA;IAKZ,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAEhC,IAAA,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;QACxB,OAAO;YACL,KAAK;YACL,IAAI;SACL,CAAC;KACH;IAED,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAE9B,IAAA,OAAO,KAAK,CAAC,MAAM,EAAE;QACnB,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACtC,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAE1C,QAAA,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,SAAS,EAAE;YACxD,OAAO,EAAE,IAAI,EAAE,CAAC;SACjB;AAED,QAAA,IAAI,UAAU,IAAI,UAAU,CAAC,IAAI,EAAE;YACjC,OAAO;AACL,gBAAA,IAAI,EAAE,SAAS;AACf,gBAAA,KAAK,EAAE,UAAU;aAClB,CAAC;SACH;QAED,KAAK,CAAC,GAAG,EAAE,CAAC;KACb;IAED,OAAO;QACL,IAAI;KACL,CAAC;AACJ;;AC3CA,qBAAe,CACb,WAAoB,EACpB,SAAkB,EAClB,WAAoB,EACpB,cAGC,EACD,IAAkC,KAChC;AACF,IAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,QAAA,OAAO,KAAK,CAAC;KACd;AAAM,SAAA,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,EAAE;AACzC,QAAA,OAAO,EAAE,SAAS,IAAI,WAAW,CAAC,CAAC;KACpC;AAAM,SAAA,IAAI,WAAW,GAAG,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE;QAChE,OAAO,CAAC,WAAW,CAAC;KACrB;AAAM,SAAA,IAAI,WAAW,GAAG,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE;AACpE,QAAA,OAAO,WAAW,CAAC;KACpB;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;;AClBD,sBAAe,CAAI,GAAM,EAAE,IAAY,KACrC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC;;ACkFrD,MAAM,cAAc,GAAG;IACrB,IAAI,EAAE,eAAe,CAAC,QAAQ;IAC9B,cAAc,EAAE,eAAe,CAAC,QAAQ;AACxC,IAAA,gBAAgB,EAAE,IAAI;CACd,CAAC;AAEK,SAAA,iBAAiB,CAI/B,KAAA,GAA8C,EAAE,EAAA;AAEhD,IAAA,IAAI,QAAQ,GAAG;AACb,QAAA,GAAG,cAAc;AACjB,QAAA,GAAG,KAAK;KACT,CAAC;AACF,IAAA,IAAI,UAAU,GAA4B;AACxC,QAAA,WAAW,EAAE,CAAC;AACd,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,SAAS,EAAE,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC;AAC7C,QAAA,YAAY,EAAE,KAAK;AACnB,QAAA,WAAW,EAAE,KAAK;AAClB,QAAA,YAAY,EAAE,KAAK;AACnB,QAAA,kBAAkB,EAAE,KAAK;AACzB,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,aAAa,EAAE,EAAE;AACjB,QAAA,WAAW,EAAE,EAAE;AACf,QAAA,gBAAgB,EAAE,EAAE;AACpB,QAAA,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,EAAE;AAC7B,QAAA,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,KAAK;KACrC,CAAC;IACF,IAAI,OAAO,GAAc,EAAE,CAAC;AAC5B,IAAA,IAAI,cAAc,GAChB,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC3D,UAAE,WAAW,CAAC,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE;UAC5D,EAAE,CAAC;AACT,IAAA,IAAI,WAAW,GAAG,QAAQ,CAAC,gBAAgB;AACzC,UAAE,EAAE;AACJ,UAAE,WAAW,CAAC,cAAc,CAAC,CAAC;AAChC,IAAA,IAAI,MAAM,GAAG;AACX,QAAA,MAAM,EAAE,KAAK;AACb,QAAA,KAAK,EAAE,KAAK;AACZ,QAAA,KAAK,EAAE,KAAK;KACb,CAAC;AACF,IAAA,IAAI,MAAM,GAAU;QAClB,KAAK,EAAE,IAAI,GAAG,EAAE;QAChB,OAAO,EAAE,IAAI,GAAG,EAAE;QAClB,KAAK,EAAE,IAAI,GAAG,EAAE;QAChB,KAAK,EAAE,IAAI,GAAG,EAAE;KACjB,CAAC;AACF,IAAA,IAAI,kBAAwC,CAAC;IAC7C,IAAI,KAAK,GAAG,CAAC,CAAC;AACd,IAAA,MAAM,eAAe,GAAkB;AACrC,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,WAAW,EAAE,KAAK;AAClB,QAAA,gBAAgB,EAAE,KAAK;AACvB,QAAA,aAAa,EAAE,KAAK;AACpB,QAAA,YAAY,EAAE,KAAK;AACnB,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,MAAM,EAAE,KAAK;KACd,CAAC;AACF,IAAA,MAAM,SAAS,GAA2B;QACxC,MAAM,EAAE,aAAa,EAAE;QACvB,KAAK,EAAE,aAAa,EAAE;QACtB,KAAK,EAAE,aAAa,EAAE;KACvB,CAAC;IACF,MAAM,0BAA0B,GAAG,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACrE,MAAM,yBAAyB,GAAG,kBAAkB,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;IAC9E,MAAM,gCAAgC,GACpC,QAAQ,CAAC,YAAY,KAAK,eAAe,CAAC,GAAG,CAAC;IAEhD,MAAM,QAAQ,GACZ,CAAqB,QAAW,KAChC,CAAC,IAAY,KAAI;QACf,YAAY,CAAC,KAAK,CAAC,CAAC;AACpB,QAAA,KAAK,GAAG,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACrC,KAAC,CAAC;AAEJ,IAAA,MAAM,YAAY,GAAG,OAAO,iBAA2B,KAAI;AACzD,QAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,eAAe,CAAC,OAAO,IAAI,iBAAiB,CAAC,EAAE;AACrE,YAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ;kBAC7B,aAAa,CAAC,CAAC,MAAM,cAAc,EAAE,EAAE,MAAM,CAAC;kBAC9C,MAAM,wBAAwB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAElD,YAAA,IAAI,OAAO,KAAK,UAAU,CAAC,OAAO,EAAE;AAClC,gBAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;oBACnB,OAAO;AACR,iBAAA,CAAC,CAAC;aACJ;SACF;AACH,KAAC,CAAC;AAEF,IAAA,MAAM,mBAAmB,GAAG,CAAC,KAAgB,EAAE,YAAsB,KAAI;QACvE,IACE,CAAC,KAAK,CAAC,QAAQ;aACd,eAAe,CAAC,YAAY,IAAI,eAAe,CAAC,gBAAgB,CAAC,EAClE;AACA,YAAA,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,KAAI;gBACnD,IAAI,IAAI,EAAE;oBACR,YAAY;0BACR,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,EAAE,YAAY,CAAC;0BACpD,KAAK,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;iBAC9C;AACH,aAAC,CAAC,CAAC;AAEH,YAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;gBACnB,gBAAgB,EAAE,UAAU,CAAC,gBAAgB;AAC7C,gBAAA,YAAY,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,CAAC;AAC1D,aAAA,CAAC,CAAC;SACJ;AACH,KAAC,CAAC;IAEF,MAAM,iBAAiB,GAA0B,CAC/C,IAAI,EACJ,MAAM,GAAG,EAAE,EACX,MAAM,EACN,IAAI,EACJ,eAAe,GAAG,IAAI,EACtB,0BAA0B,GAAG,IAAI,KAC/B;QACF,IAAI,IAAI,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACrC,YAAA,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;AACrB,YAAA,IAAI,0BAA0B,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,EAAE;AACnE,gBAAA,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrE,eAAe,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;aACpD;AAED,YAAA,IACE,0BAA0B;AAC1B,gBAAA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAC3C;gBACA,MAAM,MAAM,GAAG,MAAM,CACnB,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,EAC5B,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,IAAI,CACV,CAAC;gBACF,eAAe,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AACxD,gBAAA,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;aAC1C;YAED,IACE,eAAe,CAAC,aAAa;gBAC7B,0BAA0B;AAC1B,gBAAA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,EAClD;gBACA,MAAM,aAAa,GAAG,MAAM,CAC1B,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,EACnC,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,IAAI,CACV,CAAC;gBACF,eAAe,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;aACvE;AAED,YAAA,IAAI,eAAe,CAAC,WAAW,EAAE;gBAC/B,UAAU,CAAC,WAAW,GAAG,cAAc,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;aACtE;AAED,YAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;gBACnB,IAAI;AACJ,gBAAA,OAAO,EAAE,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC;gBAChC,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,OAAO,EAAE,UAAU,CAAC,OAAO;AAC5B,aAAA,CAAC,CAAC;SACJ;aAAM;AACL,YAAA,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;SAChC;AACH,KAAC,CAAC;AAEF,IAAA,MAAM,YAAY,GAAG,CAAC,IAAuB,EAAE,KAAiB,KAAI;QAClE,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACpC,QAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;YACnB,MAAM,EAAE,UAAU,CAAC,MAAM;AAC1B,SAAA,CAAC,CAAC;AACL,KAAC,CAAC;AAEF,IAAA,MAAM,UAAU,GAAG,CAAC,MAAiC,KAAI;AACvD,QAAA,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;AAC3B,QAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;YACnB,MAAM,EAAE,UAAU,CAAC,MAAM;AACzB,YAAA,OAAO,EAAE,KAAK;AACf,SAAA,CAAC,CAAC;AACL,KAAC,CAAC;IAEF,MAAM,mBAAmB,GAAG,CAC1B,IAAuB,EACvB,oBAA6B,EAC7B,KAAe,EACf,GAAS,KACP;QACF,MAAM,KAAK,GAAU,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAExC,IAAI,KAAK,EAAE;YACT,MAAM,YAAY,GAAG,GAAG,CACtB,WAAW,EACX,IAAI,EACJ,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,KAAK,CACvD,CAAC;YAEF,WAAW,CAAC,YAAY,CAAC;AACzB,iBAAC,GAAG,IAAK,GAAwB,CAAC,cAAc,CAAC;gBACjD,oBAAoB;kBAChB,GAAG,CACD,WAAW,EACX,IAAI,EACJ,oBAAoB,GAAG,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,CAC9D;AACH,kBAAE,aAAa,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;AAEtC,YAAA,MAAM,CAAC,KAAK,IAAI,YAAY,EAAE,CAAC;SAChC;AACH,KAAC,CAAC;AAEF,IAAA,MAAM,mBAAmB,GAAG,CAC1B,IAAuB,EACvB,UAAmB,EACnB,WAAqB,EACrB,WAAqB,EACrB,YAAsB,KAGpB;QACF,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAC9B,IAAI,eAAe,GAAG,KAAK,CAAC;AAC5B,QAAA,MAAM,MAAM,GAAwD;YAClE,IAAI;SACL,CAAC;AAEF,QAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACnB,MAAM,aAAa,GAAG,CAAC,EACrB,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC;AAClB,gBAAA,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,EAAE;gBACrB,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,QAAQ,CAC/B,CAAC;AACF,YAAA,IAAI,CAAC,WAAW,IAAI,WAAW,EAAE;AAC/B,gBAAA,IAAI,eAAe,CAAC,OAAO,EAAE;AAC3B,oBAAA,eAAe,GAAG,UAAU,CAAC,OAAO,CAAC;oBACrC,UAAU,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,SAAS,EAAE,CAAC;AAClD,oBAAA,iBAAiB,GAAG,eAAe,KAAK,MAAM,CAAC,OAAO,CAAC;iBACxD;AAED,gBAAA,MAAM,sBAAsB,GAC1B,aAAa,IAAI,SAAS,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,EAAE,UAAU,CAAC,CAAC;AAEpE,gBAAA,eAAe,GAAG,CAAC,EACjB,CAAC,aAAa,IAAI,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CACpD,CAAC;AACF,gBAAA,sBAAsB,IAAI,aAAa;sBACnC,KAAK,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC;sBACnC,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5C,gBAAA,MAAM,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC;gBAC5C,iBAAiB;oBACf,iBAAiB;yBAChB,eAAe,CAAC,WAAW;AAC1B,4BAAA,eAAe,KAAK,CAAC,sBAAsB,CAAC,CAAC;aAClD;YAED,IAAI,WAAW,EAAE;gBACf,MAAM,sBAAsB,GAAG,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;gBAEnE,IAAI,CAAC,sBAAsB,EAAE;oBAC3B,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;AACjD,oBAAA,MAAM,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;oBAChD,iBAAiB;wBACf,iBAAiB;6BAChB,eAAe,CAAC,aAAa;gCAC5B,sBAAsB,KAAK,WAAW,CAAC,CAAC;iBAC7C;aACF;YAED,iBAAiB,IAAI,YAAY,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACnE;QAED,OAAO,iBAAiB,GAAG,MAAM,GAAG,EAAE,CAAC;AACzC,KAAC,CAAC;IAEF,MAAM,mBAAmB,GAAG,CAC1B,IAAuB,EACvB,OAAiB,EACjB,KAAkB,EAClB,UAIC,KACC;QACF,MAAM,kBAAkB,GAAG,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACxD,QAAA,MAAM,iBAAiB,GACrB,eAAe,CAAC,OAAO;YACvB,SAAS,CAAC,OAAO,CAAC;AAClB,YAAA,UAAU,CAAC,OAAO,KAAK,OAAO,CAAC;AAEjC,QAAA,IAAI,KAAK,CAAC,UAAU,IAAI,KAAK,EAAE;AAC7B,YAAA,kBAAkB,GAAG,QAAQ,CAAC,MAAM,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AAC/D,YAAA,kBAAkB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;SACtC;aAAM;YACL,YAAY,CAAC,KAAK,CAAC,CAAC;YACpB,kBAAkB,GAAG,IAAI,CAAC;YAC1B,KAAK;kBACD,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC;kBACnC,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SACpC;AAED,QAAA,IACE,CAAC,KAAK,GAAG,CAAC,SAAS,CAAC,kBAAkB,EAAE,KAAK,CAAC,GAAG,kBAAkB;YACnE,CAAC,aAAa,CAAC,UAAU,CAAC;AAC1B,YAAA,iBAAiB,EACjB;AACA,YAAA,MAAM,gBAAgB,GAAG;AACvB,gBAAA,GAAG,UAAU;AACb,gBAAA,IAAI,iBAAiB,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;gBAC/D,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,IAAI;aACL,CAAC;AAEF,YAAA,UAAU,GAAG;AACX,gBAAA,GAAG,UAAU;AACb,gBAAA,GAAG,gBAAgB;aACpB,CAAC;AAEF,YAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACxC;AACH,KAAC,CAAC;AAEF,IAAA,MAAM,cAAc,GAAG,OAAO,IAA0B,KAAI;AAC1D,QAAA,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAChC,QAAA,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAS,CACrC,WAA2B,EAC3B,QAAQ,CAAC,OAAO,EAChB,kBAAkB,CAChB,IAAI,IAAI,MAAM,CAAC,KAAK,EACpB,OAAO,EACP,QAAQ,CAAC,YAAY,EACrB,QAAQ,CAAC,yBAAyB,CACnC,CACF,CAAC;QACF,mBAAmB,CAAC,IAAI,CAAC,CAAC;AAC1B,QAAA,OAAO,MAAM,CAAC;AAChB,KAAC,CAAC;AAEF,IAAA,MAAM,2BAA2B,GAAG,OAAO,KAA2B,KAAI;QACxE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,cAAc,CAAC,KAAK,CAAC,CAAC;QAE/C,IAAI,KAAK,EAAE;AACT,YAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACxB,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAChC,KAAK;sBACD,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC;sBACnC,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;aACpC;SACF;aAAM;AACL,YAAA,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;SAC5B;AAED,QAAA,OAAO,MAAM,CAAC;AAChB,KAAC,CAAC;IAEF,MAAM,wBAAwB,GAAG,OAC/B,MAAiB,EACjB,oBAA8B,EAC9B,OAEI,GAAA;AACF,QAAA,KAAK,EAAE,IAAI;AACZ,KAAA,KACC;AACF,QAAA,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE;AACzB,YAAA,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;YAE3B,IAAI,KAAK,EAAE;gBACT,MAAM,EAAE,EAAE,EAAE,GAAG,UAAU,EAAE,GAAG,KAAc,CAAC;gBAE7C,IAAI,EAAE,EAAE;AACN,oBAAA,MAAM,gBAAgB,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AACnD,oBAAA,MAAM,iBAAiB,GACrB,KAAK,CAAC,EAAE,IAAI,oBAAoB,CAAE,KAAe,CAAC,EAAE,CAAC,CAAC;AAExD,oBAAA,IAAI,iBAAiB,IAAI,eAAe,CAAC,gBAAgB,EAAE;AACzD,wBAAA,mBAAmB,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;qBACnC;oBAED,MAAM,UAAU,GAAG,MAAM,aAAa,CACpC,KAAc,EACd,WAAW,EACX,gCAAgC,EAChC,QAAQ,CAAC,yBAAyB,IAAI,CAAC,oBAAoB,EAC3D,gBAAgB,CACjB,CAAC;AAEF,oBAAA,IAAI,iBAAiB,IAAI,eAAe,CAAC,gBAAgB,EAAE;AACzD,wBAAA,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;qBAC7B;AAED,oBAAA,IAAI,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AACvB,wBAAA,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;wBACtB,IAAI,oBAAoB,EAAE;4BACxB,MAAM;yBACP;qBACF;AAED,oBAAA,CAAC,oBAAoB;AACnB,yBAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC;AACvB,8BAAE,gBAAgB;AAChB,kCAAE,yBAAyB,CACvB,UAAU,CAAC,MAAM,EACjB,UAAU,EACV,EAAE,CAAC,IAAI,CACR;AACH,kCAAE,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AACxD,8BAAE,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;iBAC1C;gBAED,CAAC,aAAa,CAAC,UAAU,CAAC;qBACvB,MAAM,wBAAwB,CAC7B,UAAU,EACV,oBAAoB,EACpB,OAAO,CACR,CAAC,CAAC;aACN;SACF;QAED,OAAO,OAAO,CAAC,KAAK,CAAC;AACvB,KAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,MAAK;AAC5B,QAAA,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,OAAO,EAAE;YACjC,MAAM,KAAK,GAAU,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAExC,KAAK;AACH,iBAAC,KAAK,CAAC,EAAE,CAAC,IAAI;AACZ,sBAAE,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;sBACxC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;gBACxB,UAAU,CAAC,IAA+B,CAAC,CAAC;SAC/C;AAED,QAAA,MAAM,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;AAC7B,KAAC,CAAC;AAEF,IAAA,MAAM,SAAS,GAAe,CAAC,IAAI,EAAE,IAAI,KACvC,CAAC,KAAK,CAAC,QAAQ;SACd,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC;YAC7C,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,cAAc,CAAC,CAAC,CAAC;AAE3C,IAAA,MAAM,SAAS,GAAgC,CAC7C,KAAK,EACL,YAAY,EACZ,QAAQ,KAER,mBAAmB,CACjB,KAAK,EACL,MAAM,EACN;QACE,IAAI,MAAM,CAAC,KAAK;AACd,cAAE,WAAW;AACb,cAAE,WAAW,CAAC,YAAY,CAAC;AACzB,kBAAE,cAAc;AAChB,kBAAE,QAAQ,CAAC,KAAK,CAAC;AACf,sBAAE,EAAE,CAAC,KAAK,GAAG,YAAY,EAAE;sBACzB,YAAY,CAAC;AACtB,KAAA,EACD,QAAQ,EACR,YAAY,CACb,CAAC;IAEJ,MAAM,cAAc,GAAG,CACrB,IAAuB,KAEvB,OAAO,CACL,GAAG,CACD,MAAM,CAAC,KAAK,GAAG,WAAW,GAAG,cAAc,EAC3C,IAAI,EACJ,KAAK,CAAC,gBAAgB,GAAG,GAAG,CAAC,cAAc,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,CAC5D,CACF,CAAC;IAEJ,MAAM,aAAa,GAAG,CACpB,IAAuB,EACvB,KAAkC,EAClC,OAAA,GAA0B,EAAE,KAC1B;QACF,MAAM,KAAK,GAAU,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACxC,IAAI,UAAU,GAAY,KAAK,CAAC;QAEhC,IAAI,KAAK,EAAE;AACT,YAAA,MAAM,cAAc,GAAG,KAAK,CAAC,EAAE,CAAC;YAEhC,IAAI,cAAc,EAAE;gBAClB,CAAC,cAAc,CAAC,QAAQ;AACtB,oBAAA,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,eAAe,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC;gBAEjE,UAAU;oBACR,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,iBAAiB,CAAC,KAAK,CAAC;AAC3D,0BAAE,EAAE;0BACF,KAAK,CAAC;AAEZ,gBAAA,IAAI,gBAAgB,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AACxC,oBAAA,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,OAAO,CACrC,CAAC,SAAS,MACP,SAAS,CAAC,QAAQ,GACjB,UACD,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAC/B,CAAC;iBACH;AAAM,qBAAA,IAAI,cAAc,CAAC,IAAI,EAAE;AAC9B,oBAAA,IAAI,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AACvC,wBAAA,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;8BAC1B,cAAc,CAAC,IAAI,CAAC,OAAO,CACzB,CAAC,WAAW,KACV,CAAC,CAAC,WAAW,CAAC,cAAc,IAAI,CAAC,WAAW,CAAC,QAAQ;iCACpD,WAAW,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC;AAC9C,sCAAE,CAAC,CAAE,UAAiB,CAAC,IAAI,CACvB,CAAC,IAAY,KAAK,IAAI,KAAK,WAAW,CAAC,KAAK,CAC7C;AACH,sCAAE,UAAU,KAAK,WAAW,CAAC,KAAK,CAAC,CACxC;AACH,8BAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;AACtB,iCAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;qBACrD;yBAAM;wBACL,cAAc,CAAC,IAAI,CAAC,OAAO,CACzB,CAAC,QAA0B,MACxB,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,KAAK,KAAK,UAAU,CAAC,CACrD,CAAC;qBACH;iBACF;AAAM,qBAAA,IAAI,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AAC1C,oBAAA,cAAc,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC;iBAC/B;qBAAM;AACL,oBAAA,cAAc,CAAC,GAAG,CAAC,KAAK,GAAG,UAAU,CAAC;AAEtC,oBAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE;AAC5B,wBAAA,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;4BACpB,IAAI;AACJ,4BAAA,MAAM,EAAE,EAAE,GAAG,WAAW,EAAE;AAC3B,yBAAA,CAAC,CAAC;qBACJ;iBACF;aACF;SACF;AAED,QAAA,CAAC,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW;AACzC,YAAA,mBAAmB,CACjB,IAAI,EACJ,UAAU,EACV,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,WAAW,EACnB,IAAI,CACL,CAAC;AAEJ,QAAA,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,IAA0B,CAAC,CAAC;AAChE,KAAC,CAAC;IAEF,MAAM,SAAS,GAAG,CAKhB,IAAO,EACP,KAAQ,EACR,OAAU,KACR;AACF,QAAA,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE;AAC5B,YAAA,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;AACnC,YAAA,MAAM,SAAS,GAAG,CAAA,EAAG,IAAI,CAAI,CAAA,EAAA,QAAQ,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAEtC,YAAA,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;gBACrB,QAAQ,CAAC,UAAU,CAAC;AACpB,iBAAC,KAAK,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACtB,CAAC,YAAY,CAAC,UAAU,CAAC;kBACrB,SAAS,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC;kBACzC,aAAa,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;SACnD;AACH,KAAC,CAAC;IAEF,MAAM,QAAQ,GAAkC,CAC9C,IAAI,EACJ,KAAK,EACL,OAAO,GAAG,EAAE,KACV;QACF,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACjC,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC5C,QAAA,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AAEtC,QAAA,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QAEnC,IAAI,YAAY,EAAE;AAChB,YAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;gBACnB,IAAI;AACJ,gBAAA,MAAM,EAAE,EAAE,GAAG,WAAW,EAAE;AAC3B,aAAA,CAAC,CAAC;YAEH,IACE,CAAC,eAAe,CAAC,OAAO,IAAI,eAAe,CAAC,WAAW;gBACvD,OAAO,CAAC,WAAW,EACnB;AACA,gBAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;oBACnB,IAAI;AACJ,oBAAA,WAAW,EAAE,cAAc,CAAC,cAAc,EAAE,WAAW,CAAC;AACxD,oBAAA,OAAO,EAAE,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC;AACrC,iBAAA,CAAC,CAAC;aACJ;SACF;aAAM;YACL,KAAK,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC;kBAChD,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC;kBACpC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;SAC9C;AAED,QAAA,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,UAAU,EAAE,CAAC,CAAC;AACnE,QAAA,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;YACpB,IAAI,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,GAAG,SAAS;AACrC,YAAA,MAAM,EAAE,EAAE,GAAG,WAAW,EAAE;AAC3B,SAAA,CAAC,CAAC;AACL,KAAC,CAAC;AAEF,IAAA,MAAM,QAAQ,GAAkB,OAAO,KAAK,KAAI;AAC9C,QAAA,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;AACpB,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AAC5B,QAAA,IAAI,IAAI,GAAG,MAAM,CAAC,IAAc,CAAC;QACjC,IAAI,mBAAmB,GAAG,IAAI,CAAC;QAC/B,MAAM,KAAK,GAAU,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACxC,MAAM,oBAAoB,GAAG,MAC3B,MAAM,CAAC,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;AAC/D,QAAA,MAAM,0BAA0B,GAAG,CAAC,UAAe,KAAU;YAC3D,mBAAmB;AACjB,gBAAA,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC;AACxB,qBAAC,YAAY,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;AACzD,oBAAA,SAAS,CAAC,UAAU,EAAE,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;AAC9D,SAAC,CAAC;QAEF,IAAI,KAAK,EAAE;AACT,YAAA,IAAI,KAAK,CAAC;AACV,YAAA,IAAI,OAAO,CAAC;AACZ,YAAA,MAAM,UAAU,GAAG,oBAAoB,EAAE,CAAC;AAC1C,YAAA,MAAM,WAAW,GACf,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,SAAS,CAAC;YAChE,MAAM,oBAAoB,GACxB,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvB,CAAC,QAAQ,CAAC,QAAQ;AAClB,gBAAA,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC;AAC7B,gBAAA,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;gBAChB,cAAc,CACZ,WAAW,EACX,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,EACnC,UAAU,CAAC,WAAW,EACtB,yBAAyB,EACzB,0BAA0B,CAC3B,CAAC;YACJ,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;AAErD,YAAA,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;YAEnC,IAAI,WAAW,EAAE;AACf,gBAAA,KAAK,CAAC,EAAE,CAAC,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC1C,gBAAA,kBAAkB,IAAI,kBAAkB,CAAC,CAAC,CAAC,CAAC;aAC7C;AAAM,iBAAA,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;AAC5B,gBAAA,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aAC1B;AAED,YAAA,MAAM,UAAU,GAAG,mBAAmB,CACpC,IAAI,EACJ,UAAU,EACV,WAAW,EACX,KAAK,CACN,CAAC;YAEF,MAAM,YAAY,GAAG,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC;AAE3D,YAAA,CAAC,WAAW;AACV,gBAAA,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;oBACpB,IAAI;oBACJ,IAAI,EAAE,KAAK,CAAC,IAAI;AAChB,oBAAA,MAAM,EAAE,EAAE,GAAG,WAAW,EAAE;AAC3B,iBAAA,CAAC,CAAC;YAEL,IAAI,oBAAoB,EAAE;AACxB,gBAAA,IAAI,eAAe,CAAC,OAAO,EAAE;AAC3B,oBAAA,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;wBAC3B,IAAI,WAAW,EAAE;AACf,4BAAA,YAAY,EAAE,CAAC;yBAChB;qBACF;yBAAM;AACL,wBAAA,YAAY,EAAE,CAAC;qBAChB;iBACF;AAED,gBAAA,QACE,YAAY;oBACZ,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,OAAO,GAAG,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,EAC9D;aACH;AAED,YAAA,CAAC,WAAW,IAAI,OAAO,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,UAAU,EAAE,CAAC,CAAC;AAEnE,YAAA,IAAI,QAAQ,CAAC,QAAQ,EAAE;gBACrB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAEhD,0BAA0B,CAAC,UAAU,CAAC,CAAC;gBAEvC,IAAI,mBAAmB,EAAE;AACvB,oBAAA,MAAM,yBAAyB,GAAG,iBAAiB,CACjD,UAAU,CAAC,MAAM,EACjB,OAAO,EACP,IAAI,CACL,CAAC;AACF,oBAAA,MAAM,iBAAiB,GAAG,iBAAiB,CACzC,MAAM,EACN,OAAO,EACP,yBAAyB,CAAC,IAAI,IAAI,IAAI,CACvC,CAAC;AAEF,oBAAA,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC;AAChC,oBAAA,IAAI,GAAG,iBAAiB,CAAC,IAAI,CAAC;AAE9B,oBAAA,OAAO,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;iBACjC;aACF;iBAAM;AACL,gBAAA,mBAAmB,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AAClC,gBAAA,KAAK,GAAG,CACN,MAAM,aAAa,CACjB,KAAK,EACL,WAAW,EACX,gCAAgC,EAChC,QAAQ,CAAC,yBAAyB,CACnC,EACD,IAAI,CAAC,CAAC;AACR,gBAAA,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBAE5B,0BAA0B,CAAC,UAAU,CAAC,CAAC;gBAEvC,IAAI,mBAAmB,EAAE;oBACvB,IAAI,KAAK,EAAE;wBACT,OAAO,GAAG,KAAK,CAAC;qBACjB;AAAM,yBAAA,IAAI,eAAe,CAAC,OAAO,EAAE;wBAClC,OAAO,GAAG,MAAM,wBAAwB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;qBACzD;iBACF;aACF;YAED,IAAI,mBAAmB,EAAE;gBACvB,KAAK,CAAC,EAAE,CAAC,IAAI;AACX,oBAAA,OAAO,CACL,KAAK,CAAC,EAAE,CAAC,IAEoB,CAC9B,CAAC;gBACJ,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;aACvD;SACF;AACH,KAAC,CAAC;AAEF,IAAA,MAAM,WAAW,GAAG,CAAC,GAAQ,EAAE,GAAW,KAAI;AAC5C,QAAA,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,EAAE;YAC5C,GAAG,CAAC,KAAK,EAAE,CAAC;AACZ,YAAA,OAAO,CAAC,CAAC;SACV;QACD,OAAO;AACT,KAAC,CAAC;IAEF,MAAM,OAAO,GAAiC,OAAO,IAAI,EAAE,OAAO,GAAG,EAAE,KAAI;AACzE,QAAA,IAAI,OAAO,CAAC;AACZ,QAAA,IAAI,gBAAgB,CAAC;AACrB,QAAA,MAAM,UAAU,GAAG,qBAAqB,CAAC,IAAI,CAAwB,CAAC;AAEtE,QAAA,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACrB,YAAA,MAAM,MAAM,GAAG,MAAM,2BAA2B,CAC9C,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,UAAU,CACtC,CAAC;AAEF,YAAA,OAAO,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;AAChC,YAAA,gBAAgB,GAAG,IAAI;AACrB,kBAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;kBAC7C,OAAO,CAAC;SACb;aAAM,IAAI,IAAI,EAAE;AACf,YAAA,gBAAgB,GAAG,CACjB,MAAM,OAAO,CAAC,GAAG,CACf,UAAU,CAAC,GAAG,CAAC,OAAO,SAAS,KAAI;gBACjC,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;gBACtC,OAAO,MAAM,wBAAwB,CACnC,KAAK,IAAI,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,SAAS,GAAG,KAAK,EAAE,GAAG,KAAK,CACnD,CAAC;aACH,CAAC,CACH,EACD,KAAK,CAAC,OAAO,CAAC,CAAC;AACjB,YAAA,EAAE,CAAC,gBAAgB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,YAAY,EAAE,CAAC;SAC/D;aAAM;YACL,gBAAgB,GAAG,OAAO,GAAG,MAAM,wBAAwB,CAAC,OAAO,CAAC,CAAC;SACtE;AAED,QAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACnB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;iBAClB,eAAe,CAAC,OAAO,IAAI,OAAO,KAAK,UAAU,CAAC,OAAO,CAAC;AACzD,kBAAE,EAAE;AACJ,kBAAE,EAAE,IAAI,EAAE,CAAC;AACb,YAAA,IAAI,QAAQ,CAAC,QAAQ,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;YAClD,MAAM,EAAE,UAAU,CAAC,MAAM;AAC1B,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO,CAAC,WAAW;AACjB,YAAA,CAAC,gBAAgB;AACjB,YAAA,qBAAqB,CACnB,OAAO,EACP,WAAW,EACX,IAAI,GAAG,UAAU,GAAG,MAAM,CAAC,KAAK,CACjC,CAAC;AAEJ,QAAA,OAAO,gBAAgB,CAAC;AAC1B,KAAC,CAAC;AAEF,IAAA,MAAM,SAAS,GAAmC,CAChD,UAE0C,KACxC;AACF,QAAA,MAAM,MAAM,GAAG;AACb,YAAA,IAAI,MAAM,CAAC,KAAK,GAAG,WAAW,GAAG,cAAc,CAAC;SACjD,CAAC;QAEF,OAAO,WAAW,CAAC,UAAU,CAAC;AAC5B,cAAE,MAAM;AACR,cAAE,QAAQ,CAAC,UAAU,CAAC;AACpB,kBAAE,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC;AACzB,kBAAE,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;AACpD,KAAC,CAAC;IAEF,MAAM,aAAa,GAAuC,CACxD,IAAI,EACJ,SAAS,MACL;AACJ,QAAA,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,IAAI,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC;AACtD,QAAA,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,IAAI,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC;AAC3D,QAAA,KAAK,EAAE,GAAG,CAAC,CAAC,SAAS,IAAI,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC;QAClD,YAAY,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC;AACtD,QAAA,SAAS,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,IAAI,UAAU,EAAE,aAAa,EAAE,IAAI,CAAC;AAChE,KAAA,CAAC,CAAC;AAEH,IAAA,MAAM,WAAW,GAAqC,CAAC,IAAI,KAAI;QAC7D,IAAI;YACF,qBAAqB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,KAC5C,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,CAAC,CACpC,CAAC;AAEJ,QAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;YACnB,MAAM,EAAE,IAAI,GAAG,UAAU,CAAC,MAAM,GAAG,EAAE;AACtC,SAAA,CAAC,CAAC;AACL,KAAC,CAAC;IAEF,MAAM,QAAQ,GAAkC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,KAAI;QACvE,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,GAAG,CAAC;AAC1D,QAAA,MAAM,YAAY,GAAG,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;;AAGxD,QAAA,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,eAAe,EAAE,GAAG,YAAY,CAAC;AAE5E,QAAA,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,EAAE;AAC3B,YAAA,GAAG,eAAe;AAClB,YAAA,GAAG,KAAK;YACR,GAAG;AACJ,SAAA,CAAC,CAAC;AAEH,QAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;YACnB,IAAI;YACJ,MAAM,EAAE,UAAU,CAAC,MAAM;AACzB,YAAA,OAAO,EAAE,KAAK;AACf,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO,IAAI,OAAO,CAAC,WAAW,IAAI,GAAG,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;AACpE,KAAC,CAAC;AAEF,IAAA,MAAM,KAAK,GAA+B,CACxC,IAG+B,EAC/B,YAAwC,KAExC,UAAU,CAAC,IAAI,CAAC;AACd,UAAE,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC;AACzB,YAAA,IAAI,EAAE,CAAC,OAAO,KACZ,IAAI,CACF,SAAS,CAAC,SAAS,EAAE,YAAY,CAAC,EAClC,OAIC,CACF;SACJ,CAAC;UACF,SAAS,CACP,IAA+C,EAC/C,YAAY,EACZ,IAAI,CACL,CAAC;IAER,MAAM,UAAU,GAAoC,CAAC,IAAI,EAAE,OAAO,GAAG,EAAE,KAAI;AACzE,QAAA,KAAK,MAAM,SAAS,IAAI,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE;AACzE,YAAA,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAC/B,YAAA,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAE/B,YAAA,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AACtB,gBAAA,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC1B,gBAAA,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;aAC/B;AAED,YAAA,CAAC,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAC1D,YAAA,CAAC,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC,UAAU,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AAC/D,YAAA,CAAC,OAAO,CAAC,WAAW,IAAI,KAAK,CAAC,UAAU,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YACnE,CAAC,OAAO,CAAC,gBAAgB;AACvB,gBAAA,KAAK,CAAC,UAAU,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;YAChD,CAAC,QAAQ,CAAC,gBAAgB;gBACxB,CAAC,OAAO,CAAC,gBAAgB;AACzB,gBAAA,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;SACpC;AAED,QAAA,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;AACpB,YAAA,MAAM,EAAE,EAAE,GAAG,WAAW,EAAE;AAC3B,SAAA,CAAC,CAAC;AAEH,QAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACnB,YAAA,GAAG,UAAU;AACb,YAAA,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,CAAC;AACxD,SAAA,CAAC,CAAC;AAEH,QAAA,CAAC,OAAO,CAAC,WAAW,IAAI,YAAY,EAAE,CAAC;AACzC,KAAC,CAAC;AAEF,IAAA,MAAM,oBAAoB,GAAkD,CAAC,EAC3E,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,MAAM,EACN,KAAK,GACN,KAAI;AACH,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,KAAK,KAAK,CAAC,CAAC,QAAQ,EAAE;YACvD,MAAM,UAAU,GAAG,QAAQ;AACzB,kBAAE,SAAS;AACX,kBAAE,WAAW,CAAC,KAAK,CAAC;sBAChB,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;sBACtD,KAAK,CAAC;AACZ,YAAA,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;YACnC,mBAAmB,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SAC3D;AACH,KAAC,CAAC;IAEF,MAAM,QAAQ,GAAkC,CAAC,IAAI,EAAE,OAAO,GAAG,EAAE,KAAI;QACrE,IAAI,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAC/B,QAAA,MAAM,iBAAiB,GACrB,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAE3D,QAAA,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE;AACjB,YAAA,IAAI,KAAK,IAAI,EAAE,CAAC;AAChB,YAAA,EAAE,EAAE;gBACF,IAAI,KAAK,IAAI,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC;gBACrD,IAAI;AACJ,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,GAAG,OAAO;AACX,aAAA;AACF,SAAA,CAAC,CAAC;AACH,QAAA,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEvB,IAAI,KAAK,EAAE;AACT,YAAA,oBAAoB,CAAC;gBACnB,KAAK;AACL,gBAAA,QAAQ,EAAE,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC;sBACjC,OAAO,CAAC,QAAQ;sBAChB,KAAK,CAAC,QAAQ;gBAClB,IAAI;gBACJ,KAAK,EAAE,OAAO,CAAC,KAAK;AACrB,aAAA,CAAC,CAAC;SACJ;aAAM;YACL,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;SAChD;QAED,OAAO;AACL,YAAA,IAAI,iBAAiB;kBACjB,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE;kBAChD,EAAE,CAAC;YACP,IAAI,QAAQ,CAAC,WAAW;AACtB,kBAAE;AACE,oBAAA,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ;AAC5B,oBAAA,GAAG,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC;AAC9B,oBAAA,GAAG,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC;AAC9B,oBAAA,SAAS,EAAE,YAAY,CAAS,OAAO,CAAC,SAAS,CAAW;AAC5D,oBAAA,SAAS,EAAE,YAAY,CAAC,OAAO,CAAC,SAAS,CAAW;AACpD,oBAAA,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,OAAO,CAAW;AACjD,iBAAA;kBACD,EAAE,CAAC;YACP,IAAI;YACJ,QAAQ;AACR,YAAA,MAAM,EAAE,QAAQ;AAChB,YAAA,GAAG,EAAE,CAAC,GAA4B,KAAU;gBAC1C,IAAI,GAAG,EAAE;AACP,oBAAA,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACxB,oBAAA,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAE3B,oBAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC;0BACnC,GAAG,CAAC,gBAAgB;8BACjB,GAAG,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAS,IAAI,GAAG;AAClE,8BAAE,GAAG;0BACL,GAAG,CAAC;AACR,oBAAA,MAAM,eAAe,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;oBACpD,MAAM,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC;AAEjC,oBAAA,IACE,eAAe;AACb,0BAAE,IAAI,CAAC,IAAI,CAAC,CAAC,MAAW,KAAK,MAAM,KAAK,QAAQ,CAAC;0BAC/C,QAAQ,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,EAC7B;wBACA,OAAO;qBACR;AAED,oBAAA,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE;AACjB,wBAAA,EAAE,EAAE;4BACF,GAAG,KAAK,CAAC,EAAE;AACX,4BAAA,IAAI,eAAe;AACjB,kCAAE;AACE,oCAAA,IAAI,EAAE;AACJ,wCAAA,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;wCACpB,QAAQ;wCACR,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;AAC1D,qCAAA;oCACD,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE;AACnC,iCAAA;AACH,kCAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;AACvB,yBAAA;AACF,qBAAA,CAAC,CAAC;oBAEH,mBAAmB,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;iBACvD;qBAAM;oBACL,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AAE/B,oBAAA,IAAI,KAAK,CAAC,EAAE,EAAE;AACZ,wBAAA,KAAK,CAAC,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC;qBACxB;AAED,oBAAA,CAAC,QAAQ,CAAC,gBAAgB,IAAI,OAAO,CAAC,gBAAgB;AACpD,wBAAA,EAAE,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC;AAC1D,wBAAA,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;iBAC5B;aACF;SACF,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,MAAM,WAAW,GAAG,MAClB,QAAQ,CAAC,gBAAgB;QACzB,qBAAqB,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;AAE5D,IAAA,MAAM,YAAY,GAAG,CAAC,QAAkB,KAAI;AAC1C,QAAA,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE;YACvB,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;YACnC,qBAAqB,CACnB,OAAO,EACP,CAAC,GAAG,EAAE,IAAI,KAAI;gBACZ,MAAM,YAAY,GAAU,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC/C,IAAI,YAAY,EAAE;oBAChB,GAAG,CAAC,QAAQ,GAAG,YAAY,CAAC,EAAE,CAAC,QAAQ,IAAI,QAAQ,CAAC;oBAEpD,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;wBACvC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAI;4BACxC,QAAQ,CAAC,QAAQ,GAAG,YAAY,CAAC,EAAE,CAAC,QAAQ,IAAI,QAAQ,CAAC;AAC3D,yBAAC,CAAC,CAAC;qBACJ;iBACF;AACH,aAAC,EACD,CAAC,EACD,KAAK,CACN,CAAC;SACH;AACH,KAAC,CAAC;AAEF,IAAA,MAAM,YAAY,GAChB,CAAC,OAAO,EAAE,SAAS,KAAK,OAAO,CAAC,KAAI;QAClC,IAAI,YAAY,GAAG,SAAS,CAAC;QAC7B,IAAI,CAAC,EAAE;AACL,YAAA,CAAC,CAAC,cAAc,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;AACvC,YAAA,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;SAC1B;AACD,QAAA,IAAI,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;AAE3C,QAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACnB,YAAA,YAAY,EAAE,IAAI;AACnB,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,QAAQ,CAAC,QAAQ,EAAE;YACrB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,cAAc,EAAE,CAAC;AAClD,YAAA,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;YAC3B,WAAW,GAAG,MAAM,CAAC;SACtB;aAAM;AACL,YAAA,MAAM,wBAAwB,CAAC,OAAO,CAAC,CAAC;SACzC;AAED,QAAA,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAEjC,QAAA,IAAI,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;AACpC,YAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACnB,gBAAA,MAAM,EAAE,EAAE;AACX,aAAA,CAAC,CAAC;AACH,YAAA,IAAI;AACF,gBAAA,MAAM,OAAO,CAAC,WAA2B,EAAE,CAAC,CAAC,CAAC;aAC/C;YAAC,OAAO,KAAK,EAAE;gBACd,YAAY,GAAG,KAAK,CAAC;aACtB;SACF;aAAM;YACL,IAAI,SAAS,EAAE;gBACb,MAAM,SAAS,CAAC,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;aAC9C;AACD,YAAA,WAAW,EAAE,CAAC;YACd,UAAU,CAAC,WAAW,CAAC,CAAC;SACzB;AAED,QAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACnB,YAAA,WAAW,EAAE,IAAI;AACjB,YAAA,YAAY,EAAE,KAAK;YACnB,kBAAkB,EAAE,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY;AACrE,YAAA,WAAW,EAAE,UAAU,CAAC,WAAW,GAAG,CAAC;YACvC,MAAM,EAAE,UAAU,CAAC,MAAM;AAC1B,SAAA,CAAC,CAAC;QACH,IAAI,YAAY,EAAE;AAChB,YAAA,MAAM,YAAY,CAAC;SACpB;AACH,KAAC,CAAC;IAEJ,MAAM,UAAU,GAAoC,CAAC,IAAI,EAAE,OAAO,GAAG,EAAE,KAAI;AACzE,QAAA,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;AACtB,YAAA,IAAI,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;AACrC,gBAAA,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;aACxD;iBAAM;AACL,gBAAA,QAAQ,CACN,IAAI,EACJ,OAAO,CAAC,YAGP,CACF,CAAC;AACF,gBAAA,GAAG,CAAC,cAAc,EAAE,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;aAC9D;AAED,YAAA,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;AACxB,gBAAA,KAAK,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;aACvC;AAED,YAAA,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AACtB,gBAAA,KAAK,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AACpC,gBAAA,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC,YAAY;AACvC,sBAAE,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;sBACvD,SAAS,EAAE,CAAC;aACjB;AAED,YAAA,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AACtB,gBAAA,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC/B,gBAAA,eAAe,CAAC,OAAO,IAAI,YAAY,EAAE,CAAC;aAC3C;YAED,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,UAAU,EAAE,CAAC,CAAC;SACzC;AACH,KAAC,CAAC;IAEF,MAAM,MAAM,GAA+B,CACzC,UAAU,EACV,gBAAgB,GAAG,EAAE,KACnB;AACF,QAAA,MAAM,aAAa,GAAG,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC,GAAG,cAAc,CAAC;AAC5E,QAAA,MAAM,kBAAkB,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC;AACtD,QAAA,MAAM,kBAAkB,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC;QACrD,MAAM,MAAM,GAAG,kBAAkB,GAAG,cAAc,GAAG,kBAAkB,CAAC;AAExE,QAAA,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE;YACvC,cAAc,GAAG,aAAa,CAAC;SAChC;AAED,QAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE;AAChC,YAAA,IAAI,gBAAgB,CAAC,eAAe,EAAE;AACpC,gBAAA,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC;oBAC5B,GAAG,MAAM,CAAC,KAAK;oBACf,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;AAC5D,iBAAA,CAAC,CAAC;gBACH,KAAK,MAAM,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;AACjD,oBAAA,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,SAAS,CAAC;AACpC,0BAAE,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AACrD,0BAAE,QAAQ,CACN,SAAoC,EACpC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CACvB,CAAC;iBACP;aACF;iBAAM;AACL,gBAAA,IAAI,KAAK,IAAI,WAAW,CAAC,UAAU,CAAC,EAAE;AACpC,oBAAA,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE;wBAC/B,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACjC,wBAAA,IAAI,KAAK,IAAI,KAAK,CAAC,EAAE,EAAE;4BACrB,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC;kCAC/C,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;AAClB,kCAAE,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC;AAEjB,4BAAA,IAAI,aAAa,CAAC,cAAc,CAAC,EAAE;gCACjC,MAAM,IAAI,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gCAC5C,IAAI,IAAI,EAAE;oCACR,IAAI,CAAC,KAAK,EAAE,CAAC;oCACb,MAAM;iCACP;6BACF;yBACF;qBACF;iBACF;gBAED,OAAO,GAAG,EAAE,CAAC;aACd;YAED,WAAW,GAAG,KAAK,CAAC,gBAAgB;kBAChC,gBAAgB,CAAC,iBAAiB;AAClC,sBAAE,WAAW,CAAC,cAAc,CAAC;AAC7B,sBAAE,EAAE;AACN,kBAAE,WAAW,CAAC,MAAM,CAAC,CAAC;AAExB,YAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACnB,gBAAA,MAAM,EAAE,EAAE,GAAG,MAAM,EAAE;AACtB,aAAA,CAAC,CAAC;AAEH,YAAA,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;AACpB,gBAAA,MAAM,EAAE,EAAE,GAAG,MAAM,EAAE;AACtB,aAAA,CAAC,CAAC;SACJ;AAED,QAAA,MAAM,GAAG;AACP,YAAA,KAAK,EAAE,gBAAgB,CAAC,eAAe,GAAG,MAAM,CAAC,KAAK,GAAG,IAAI,GAAG,EAAE;YAClE,OAAO,EAAE,IAAI,GAAG,EAAE;YAClB,KAAK,EAAE,IAAI,GAAG,EAAE;YAChB,KAAK,EAAE,IAAI,GAAG,EAAE;AAChB,YAAA,QAAQ,EAAE,KAAK;AACf,YAAA,KAAK,EAAE,EAAE;SACV,CAAC;AAEF,QAAA,MAAM,CAAC,KAAK;YACV,CAAC,eAAe,CAAC,OAAO;gBACxB,CAAC,CAAC,gBAAgB,CAAC,WAAW;AAC9B,gBAAA,CAAC,CAAC,gBAAgB,CAAC,eAAe,CAAC;QAErC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC;AAExC,QAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;YACnB,WAAW,EAAE,gBAAgB,CAAC,eAAe;kBACzC,UAAU,CAAC,WAAW;AACxB,kBAAE,CAAC;AACL,YAAA,OAAO,EAAE,kBAAkB;AACzB,kBAAE,KAAK;kBACL,gBAAgB,CAAC,SAAS;sBACxB,UAAU,CAAC,OAAO;AACpB,sBAAE,CAAC,EACC,gBAAgB,CAAC,iBAAiB;AAClC,wBAAA,CAAC,SAAS,CAAC,UAAU,EAAE,cAAc,CAAC,CACvC;YACP,WAAW,EAAE,gBAAgB,CAAC,eAAe;kBACzC,UAAU,CAAC,WAAW;AACxB,kBAAE,KAAK;AACT,YAAA,WAAW,EAAE,kBAAkB;AAC7B,kBAAE,EAAE;kBACF,gBAAgB,CAAC,eAAe;AAChC,sBAAE,gBAAgB,CAAC,iBAAiB,IAAI,WAAW;AACjD,0BAAE,cAAc,CAAC,cAAc,EAAE,WAAW,CAAC;0BAC3C,UAAU,CAAC,WAAW;AAC1B,sBAAE,gBAAgB,CAAC,iBAAiB,IAAI,UAAU;AAChD,0BAAE,cAAc,CAAC,cAAc,EAAE,UAAU,CAAC;0BAC1C,gBAAgB,CAAC,SAAS;8BACxB,UAAU,CAAC,WAAW;AACxB,8BAAE,EAAE;YACZ,aAAa,EAAE,gBAAgB,CAAC,WAAW;kBACvC,UAAU,CAAC,aAAa;AAC1B,kBAAE,EAAE;AACN,YAAA,MAAM,EAAE,gBAAgB,CAAC,UAAU,GAAG,UAAU,CAAC,MAAM,GAAG,EAAE;YAC5D,kBAAkB,EAAE,gBAAgB,CAAC,sBAAsB;kBACvD,UAAU,CAAC,kBAAkB;AAC/B,kBAAE,KAAK;AACT,YAAA,YAAY,EAAE,KAAK;AACpB,SAAA,CAAC,CAAC;AACL,KAAC,CAAC;AAEF,IAAA,MAAM,KAAK,GAA+B,CAAC,UAAU,EAAE,gBAAgB,KACrE,MAAM,CACJ,UAAU,CAAC,UAAU,CAAC;AACpB,UAAG,UAAuB,CAAC,WAA2B,CAAC;AACvD,UAAE,UAAU,EACd,gBAAgB,CACjB,CAAC;IAEJ,MAAM,QAAQ,GAAkC,CAAC,IAAI,EAAE,OAAO,GAAG,EAAE,KAAI;QACrE,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACjC,QAAA,MAAM,cAAc,GAAG,KAAK,IAAI,KAAK,CAAC,EAAE,CAAC;QAEzC,IAAI,cAAc,EAAE;AAClB,YAAA,MAAM,QAAQ,GAAG,cAAc,CAAC,IAAI;AAClC,kBAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;AACxB,kBAAE,cAAc,CAAC,GAAG,CAAC;AAEvB,YAAA,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAClB,QAAQ,CAAC,KAAK,EAAE,CAAC;AACjB,gBAAA,OAAO,CAAC,YAAY,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;aAC3C;SACF;AACH,KAAC,CAAC;AAEF,IAAA,MAAM,gBAAgB,GAAG,CACvB,gBAAkD,KAChD;AACF,QAAA,UAAU,GAAG;AACX,YAAA,GAAG,UAAU;AACb,YAAA,GAAG,gBAAgB;SACpB,CAAC;AACJ,KAAC,CAAC;IAEF,MAAM,mBAAmB,GAAG,MAC1B,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC;QACjC,QAAQ,CAAC,aAA0B,EAAE,CAAC,IAAI,CAAC,CAAC,MAAoB,KAAI;AACnE,YAAA,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;AACrC,YAAA,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACnB,gBAAA,SAAS,EAAE,KAAK;AACjB,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;IAEL,OAAO;AACL,QAAA,OAAO,EAAE;YACP,QAAQ;YACR,UAAU;YACV,aAAa;YACb,YAAY;YACZ,QAAQ;YACR,cAAc;YACd,SAAS;YACT,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,iBAAiB;YACjB,oBAAoB;YACpB,cAAc;YACd,MAAM;YACN,mBAAmB;YACnB,gBAAgB;YAChB,YAAY;YACZ,SAAS;YACT,eAAe;YACf,UAAU;AACV,YAAA,IAAI,OAAO,GAAA;AACT,gBAAA,OAAO,OAAO,CAAC;aAChB;AACD,YAAA,IAAI,WAAW,GAAA;AACb,gBAAA,OAAO,WAAW,CAAC;aACpB;AACD,YAAA,IAAI,MAAM,GAAA;AACR,gBAAA,OAAO,MAAM,CAAC;aACf;YACD,IAAI,MAAM,CAAC,KAAK,EAAA;gBACd,MAAM,GAAG,KAAK,CAAC;aAChB;AACD,YAAA,IAAI,cAAc,GAAA;AAChB,gBAAA,OAAO,cAAc,CAAC;aACvB;AACD,YAAA,IAAI,MAAM,GAAA;AACR,gBAAA,OAAO,MAAM,CAAC;aACf;YACD,IAAI,MAAM,CAAC,KAAK,EAAA;gBACd,MAAM,GAAG,KAAK,CAAC;aAChB;AACD,YAAA,IAAI,UAAU,GAAA;AACZ,gBAAA,OAAO,UAAU,CAAC;aACnB;YACD,IAAI,UAAU,CAAC,KAAK,EAAA;gBAClB,UAAU,GAAG,KAAK,CAAC;aACpB;AACD,YAAA,IAAI,QAAQ,GAAA;AACV,gBAAA,OAAO,QAAQ,CAAC;aACjB;YACD,IAAI,QAAQ,CAAC,KAAK,EAAA;AAChB,gBAAA,QAAQ,GAAG;AACT,oBAAA,GAAG,QAAQ;AACX,oBAAA,GAAG,KAAK;iBACT,CAAC;aACH;AACF,SAAA;QACD,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,KAAK;QACL,QAAQ;QACR,SAAS;QACT,KAAK;QACL,UAAU;QACV,WAAW;QACX,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,aAAa;KACd,CAAC;AACJ;;ACl7CA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACa,SAAA,OAAO,CAKrB,KAAA,GAA8C,EAAE,EAAA;AAEhD,IAAA,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,EAE9B,CAAC;AACJ,IAAA,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,EAAuB,CAAC;IACpD,MAAM,CAAC,SAAS,EAAE,eAAe,CAAC,GAAG,KAAK,CAAC,QAAQ,CAA0B;AAC3E,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,YAAY,EAAE,KAAK;AACnB,QAAA,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC;AAC1C,QAAA,WAAW,EAAE,KAAK;AAClB,QAAA,YAAY,EAAE,KAAK;AACnB,QAAA,kBAAkB,EAAE,KAAK;AACzB,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,WAAW,EAAE,CAAC;AACd,QAAA,WAAW,EAAE,EAAE;AACf,QAAA,aAAa,EAAE,EAAE;AACjB,QAAA,gBAAgB,EAAE,EAAE;AACpB,QAAA,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE;AAC1B,QAAA,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK;AACjC,QAAA,aAAa,EAAE,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC;AAC5C,cAAE,SAAS;cACT,KAAK,CAAC,aAAa;AACxB,KAAA,CAAC,CAAC;AAEH,IAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;QACzB,YAAY,CAAC,OAAO,GAAG;YACrB,GAAG,iBAAiB,CAAC,KAAK,CAAC;YAC3B,SAAS;SACV,CAAC;KACH;AAED,IAAA,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC;AAC7C,IAAA,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;AAEzB,IAAA,YAAY,CAAC;AACX,QAAA,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK;AAChC,QAAA,IAAI,EAAE,CACJ,KAAsE,KACpE;AACF,YAAA,IACE,qBAAqB,CACnB,KAAK,EACL,OAAO,CAAC,eAAe,EACvB,OAAO,CAAC,gBAAgB,EACxB,IAAI,CACL,EACD;gBACA,eAAe,CAAC,EAAE,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;aAC5C;SACF;AACF,KAAA,CAAC,CAAC;IAEH,KAAK,CAAC,SAAS,CACb,MAAM,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,EAC1C,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,CAC1B,CAAC;AAEF,IAAA,KAAK,CAAC,SAAS,CAAC,MAAK;AACnB,QAAA,IAAI,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE;AACnC,YAAA,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;AACpC,YAAA,IAAI,OAAO,KAAK,SAAS,CAAC,OAAO,EAAE;AACjC,gBAAA,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;oBAC3B,OAAO;AACR,iBAAA,CAAC,CAAC;aACJ;SACF;KACF,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;AAEjC,IAAA,KAAK,CAAC,SAAS,CAAC,MAAK;AACnB,QAAA,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE;AAC7D,YAAA,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;AAC5D,YAAA,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;AAC/B,YAAA,eAAe,CAAC,CAAC,KAAK,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;SAC5C;aAAM;YACL,OAAO,CAAC,mBAAmB,EAAE,CAAC;SAC/B;KACF,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;AAE5B,IAAA,KAAK,CAAC,SAAS,CAAC,MAAK;AACnB,QAAA,IAAI,KAAK,CAAC,MAAM,EAAE;AAChB,YAAA,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;SAClC;KACF,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;AAE5B,IAAA,KAAK,CAAC,SAAS,CAAC,MAAK;AACnB,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;YACzB,OAAO,CAAC,YAAY,EAAE,CAAC;AACvB,YAAA,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;SAC7B;AAED,QAAA,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;AACxB,YAAA,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,YAAA,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;SACzD;QAED,OAAO,CAAC,gBAAgB,EAAE,CAAC;AAC7B,KAAC,CAAC,CAAC;AAEH,IAAA,KAAK,CAAC,SAAS,CAAC,MAAK;AACnB,QAAA,KAAK,CAAC,gBAAgB;AACpB,YAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;AAC5B,gBAAA,MAAM,EAAE,OAAO,CAAC,SAAS,EAAE;AAC5B,aAAA,CAAC,CAAC;KACN,EAAE,CAAC,KAAK,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC,CAAC;AAEtC,IAAA,KAAK,CAAC,SAAS,CAAC,MAAK;AACnB,QAAA,IAAI,YAAY,CAAC,OAAO,EAAE;AACxB,YAAA,YAAY,CAAC,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAClE;AACH,KAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;IAEhB,YAAY,CAAC,OAAO,CAAC,SAAS,GAAG,iBAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAEvE,OAAO,YAAY,CAAC,OAAO,CAAC;AAC9B;;;;"}